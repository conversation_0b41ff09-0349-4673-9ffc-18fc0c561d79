import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email").notNull().unique(),
  role: text("role").notNull().default("user"), // 'admin' or 'user'
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Language translations table
export const translations = pgTable("translations", {
  id: serial("id").primaryKey(),
  key: text("key").notNull(),
  language: text("language").notNull(),
  value: text("value").notNull(),
  section: text("section").notNull(), // 'homepage', 'contact', 'documentation', etc.
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Custom code injection table
export const codeSnippets = pgTable("code_snippets", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type").notNull(), // 'head', 'body', 'footer'
  code: text("code").notNull(),
  enabled: boolean("enabled").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// SEO keyword tracking table
export const keywords = pgTable("keywords", {
  id: serial("id").primaryKey(),
  keyword: text("keyword").notNull(),
  language: text("language").notNull(),
  currentRank: integer("current_rank"),
  previousRank: integer("previous_rank"),
  url: text("url"),
  lastChecked: timestamp("last_checked"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Contact form submissions
export const contactSubmissions = pgTable("contact_submissions", {
  id: serial("id").primaryKey(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  email: text("email").notNull(),
  company: text("company"),
  message: text("message").notNull(),
  language: text("language").notNull(),
  status: text("status").notNull().default("new"), // 'new', 'read', 'replied'
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Analytics data table
export const analytics = pgTable("analytics", {
  id: serial("id").primaryKey(),
  date: timestamp("date").notNull(),
  language: text("language").notNull(),
  pageViews: integer("page_views").default(0),
  uniqueVisitors: integer("unique_visitors").default(0),
  conversions: integer("conversions").default(0),
  bounceRate: integer("bounce_rate").default(0),
  avgSessionDuration: integer("avg_session_duration").default(0),
});

// Documentation articles
export const documentation = pgTable("documentation", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  slug: text("slug").notNull().unique(),
  content: text("content").notNull(),
  category: text("category").notNull(), // 'android', 'ios', 'smart-tv', 'pc-mac'
  language: text("language").notNull(),
  difficulty: text("difficulty").notNull(), // 'beginner', 'intermediate', 'advanced'
  estimatedTime: integer("estimated_time"), // in minutes
  published: boolean("published").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  contactSubmissions: many(contactSubmissions),
}));

export const translationsRelations = relations(translations, ({ one }) => ({
  user: one(users),
}));

// Insert schemas
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  email: true,
  role: true,
});

export const insertTranslationSchema = createInsertSchema(translations).pick({
  key: true,
  language: true,
  value: true,
  section: true,
});

export const insertCodeSnippetSchema = createInsertSchema(codeSnippets).pick({
  name: true,
  type: true,
  code: true,
  enabled: true,
});

export const insertKeywordSchema = createInsertSchema(keywords).pick({
  keyword: true,
  language: true,
  currentRank: true,
  previousRank: true,
  url: true,
});

export const insertContactSubmissionSchema = createInsertSchema(contactSubmissions).pick({
  firstName: true,
  lastName: true,
  email: true,
  company: true,
  message: true,
  language: true,
});

export const insertAnalyticsSchema = createInsertSchema(analytics).pick({
  date: true,
  language: true,
  pageViews: true,
  uniqueVisitors: true,
  conversions: true,
  bounceRate: true,
  avgSessionDuration: true,
});

export const insertDocumentationSchema = createInsertSchema(documentation).pick({
  title: true,
  slug: true,
  content: true,
  category: true,
  language: true,
  difficulty: true,
  estimatedTime: true,
  published: true,
});

// Types
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export type InsertTranslation = z.infer<typeof insertTranslationSchema>;
export type Translation = typeof translations.$inferSelect;

export type InsertCodeSnippet = z.infer<typeof insertCodeSnippetSchema>;
export type CodeSnippet = typeof codeSnippets.$inferSelect;

export type InsertKeyword = z.infer<typeof insertKeywordSchema>;
export type Keyword = typeof keywords.$inferSelect;

export type InsertContactSubmission = z.infer<typeof insertContactSubmissionSchema>;
export type ContactSubmission = typeof contactSubmissions.$inferSelect;

export type InsertAnalytics = z.infer<typeof insertAnalyticsSchema>;
export type Analytics = typeof analytics.$inferSelect;

export type InsertDocumentation = z.infer<typeof insertDocumentationSchema>;
export type Documentation = typeof documentation.$inferSelect;
