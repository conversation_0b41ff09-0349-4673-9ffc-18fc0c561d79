#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 IPTV Premium Platform Quick Setup');
console.log('=====================================\n');

// Check if .env exists and has a valid DATABASE_URL
const envPath = path.join(path.dirname(__dirname), '.env');
let needsDatabase = true;

if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const dbUrlMatch = envContent.match(/DATABASE_URL=(.+)/);

  if (dbUrlMatch && dbUrlMatch[1] && !dbUrlMatch[1].includes('example') && !dbUrlMatch[1].includes('AbC123XyZ')) {
    needsDatabase = false;
    console.log('✅ Database URL found in .env file');
  }
}

if (needsDatabase) {
  console.log('⚠️  You need to set up a database first!');
  console.log('\n📋 Quick Database Setup Options:');
  console.log('\n1. 🌐 Neon (Recommended - Free & Easy):');
  console.log('   • Go to https://neon.tech');
  console.log('   • Create a free account');
  console.log('   • Create a new project');
  console.log('   • Copy the connection string');
  console.log('   • Replace DATABASE_URL in .env file');

  console.log('\n2. 🐳 Docker (If you have Docker installed):');
  console.log('   • Run: docker run --name iptv-postgres -e POSTGRES_PASSWORD=password -e POSTGRES_DB=iptv_premium -p 5432:5432 -d postgres:15');
  console.log('   • Update .env: DATABASE_URL=postgresql://postgres:password@localhost:5432/iptv_premium');

  console.log('\n3. 💻 Local PostgreSQL:');
  console.log('   • Install PostgreSQL on your system');
  console.log('   • Create database: createdb iptv_premium');
  console.log('   • Update .env with your credentials');

  console.log('\n🔧 After setting up the database, run: npm run setup');
  console.log('📱 Then start the app with: npm run dev');

  process.exit(0);
}

try {
  console.log('📦 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });

  console.log('\n🗄️  Setting up database schema...');
  execSync('npm run db:push', { stdio: 'inherit' });

  console.log('\n🌱 Seeding database with initial data...');
  execSync('npm run db:seed', { stdio: 'inherit' });

  console.log('\n✅ Setup completed successfully!');
  console.log('\n🚀 Start the development server with:');
  console.log('   npm run dev');
  console.log('\n🌐 Then open: http://localhost:5000');
  console.log('\n👤 Admin login:');
  console.log('   Username: admin');
  console.log('   Password: admin123');
  console.log('   Admin panel: http://localhost:5000/admin');

} catch (error) {
  console.error('\n❌ Setup failed:', error.message);
  console.log('\n🔧 Manual setup steps:');
  console.log('1. Make sure your DATABASE_URL in .env is correct');
  console.log('2. Run: npm run db:push');
  console.log('3. Run: npm run db:seed');
  console.log('4. Run: npm run dev');
  process.exit(1);
}
