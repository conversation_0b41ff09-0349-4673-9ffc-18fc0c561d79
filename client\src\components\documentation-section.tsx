import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/hooks/use-language';
import { DOCUMENTATION_CATEGORIES } from '@/lib/constants';
import { Clock, Star, Languages, Play, ArrowRight } from 'lucide-react';
import { Link } from 'wouter';

export function DocumentationSection() {
  const { t } = useLanguage();

  return (
    <section id="documentation" className="py-24 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6">{t('docs.title')}</h2>
          <p className="text-xl text-muted-foreground">
            {t('docs.subtitle')}
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {DOCUMENTATION_CATEGORIES.map((category) => (
            <Card key={category.id} className="glass-effect border-netflix-grey card-hover text-center">
              <CardContent className="p-6">
                <div className="text-4xl mb-4">
                  <i className={category.icon}></i>
                </div>
                <h3 className="text-lg font-semibold mb-3">{category.name}</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  {category.id === 'android' && 'Complete setup guide for Android TV, smartphones, and tablets'}
                  {category.id === 'ios' && 'Setup instructions for iPhone, iPad, and Apple TV'}
                  {category.id === 'smart-tv' && 'Configuration for Samsung, LG, Sony, and other smart TVs'}
                  {category.id === 'pc-mac' && 'Desktop applications and browser-based solutions'}
                </p>
                <Link href={`/documentation?category=${category.id}`}>
                  <Button variant="ghost" size="sm" className="text-primary hover:text-primary/80">
                    {t('docs.cta')} <ArrowRight className="w-4 h-4 ml-1" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Featured Tutorial Preview */}
        <Card className="glass-effect border-netflix-grey">
          <CardContent className="p-8">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-2xl font-bold mb-4">{t('docs.featured.title')}</h3>
                <p className="text-muted-foreground mb-6">
                  {t('docs.featured.description')}
                </p>
                
                <div className="space-y-3 mb-6">
                  <div className="flex items-center text-sm">
                    <Clock className="w-5 h-5 text-primary mr-3" />
                    <span>{t('docs.featured.time')}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Star className="w-5 h-5 text-primary mr-3" />
                    <span>{t('docs.featured.difficulty')}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Languages className="w-5 h-5 text-primary mr-3" />
                    <span>{t('docs.featured.languages')}</span>
                  </div>
                </div>
                
                <Link href="/documentation/android-tv-setup">
                  <Button className="bg-primary hover:bg-primary/90">
                    <Play className="w-5 h-5 mr-2" />
                    {t('docs.featured.cta')}
                  </Button>
                </Link>
              </div>
              
              <div className="relative">
                <img 
                  src="https://images.unsplash.com/photo-1593305841991-05c297ba4575?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=800" 
                  alt="Android TV interface showing IPTV app installation process" 
                  className="rounded-xl shadow-lg w-full h-auto" 
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
