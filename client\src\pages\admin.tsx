import { useState } from 'react';
import { Navigation } from '@/components/navigation';
import { Footer } from '@/components/footer';
import { ContentEditor } from '@/components/content-editor';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useSEO } from '@/hooks/use-seo';
import { useLanguage } from '@/hooks/use-language';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { 
  Code, 
  Settings, 
  BarChart3, 
  Mail, 
  Languages, 
  Trash2, 
  Edit, 
  Plus, 
  Save,
  Eye,
  EyeOff,
  TrendingUp,
  TrendingDown,
  Users,
  Activity,
  Globe,
  Search,
  Edit3
} from 'lucide-react';

interface CodeSnippet {
  id: number;
  name: string;
  type: 'head' | 'body' | 'footer';
  code: string;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ContactSubmission {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  message: string;
  language: string;
  status: string;
  createdAt: string;
}

interface Keyword {
  id: number;
  keyword: string;
  language: string;
  currentRank: number;
  previousRank: number;
  url: string;
  lastChecked: string;
  createdAt: string;
}

export default function Admin() {
  const { t, currentLanguage } = useLanguage();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [activeTab, setActiveTab] = useState('content');
  const [newCodeSnippet, setNewCodeSnippet] = useState({
    name: '',
    type: 'head' as const,
    code: '',
    enabled: true,
  });

  useSEO({
    title: 'Admin Dashboard - IPTV Premium',
    description: 'IPTV Premium admin dashboard for managing code snippets, analytics, and platform settings.',
    keywords: 'IPTV Premium admin, dashboard, code injection, analytics, management',
  });

  // Fetch data
  const { data: codeSnippets = [], isLoading: isLoadingSnippets } = useQuery({
    queryKey: ['/api/code-snippets'],
  });

  const { data: contactSubmissions = [], isLoading: isLoadingContacts } = useQuery({
    queryKey: ['/api/contact-submissions'],
  });

  const { data: keywords = [], isLoading: isLoadingKeywords } = useQuery({
    queryKey: ['/api/keywords'],
  });

  const { data: analytics = [], isLoading: isLoadingAnalytics } = useQuery({
    queryKey: ['/api/analytics'],
  });

  // Mutations
  const createSnippetMutation = useMutation({
    mutationFn: async (data: typeof newCodeSnippet) => {
      return apiRequest('POST', '/api/code-snippets', data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/code-snippets'] });
      setNewCodeSnippet({ name: '', type: 'head', code: '', enabled: true });
      toast({ title: 'Success', description: 'Code snippet created successfully' });
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to create code snippet', variant: 'destructive' });
    },
  });

  const updateSnippetMutation = useMutation({
    mutationFn: async ({ id, enabled }: { id: number; enabled: boolean }) => {
      return apiRequest('PUT', `/api/code-snippets/${id}`, { enabled });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/code-snippets'] });
      toast({ title: 'Success', description: 'Code snippet updated successfully' });
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to update code snippet', variant: 'destructive' });
    },
  });

  const deleteSnippetMutation = useMutation({
    mutationFn: async (id: number) => {
      return apiRequest('DELETE', `/api/code-snippets/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/code-snippets'] });
      toast({ title: 'Success', description: 'Code snippet deleted successfully' });
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to delete code snippet', variant: 'destructive' });
    },
  });

  const updateContactStatusMutation = useMutation({
    mutationFn: async ({ id, status }: { id: number; status: string }) => {
      return apiRequest('PUT', `/api/contact-submissions/${id}/status`, { status });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/contact-submissions'] });
      toast({ title: 'Success', description: 'Contact status updated successfully' });
    },
    onError: () => {
      toast({ title: 'Error', description: 'Failed to update contact status', variant: 'destructive' });
    },
  });

  const handleCreateSnippet = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newCodeSnippet.name || !newCodeSnippet.code) {
      toast({ title: 'Error', description: 'Name and code are required', variant: 'destructive' });
      return;
    }
    createSnippetMutation.mutate(newCodeSnippet);
  };

  // Dashboard Statistics
  const totalSubmissions = contactSubmissions.length;
  const newSubmissions = contactSubmissions.filter((s: ContactSubmission) => s.status === 'new').length;
  const totalKeywords = keywords.length;
  const improvingKeywords = keywords.filter((k: Keyword) => 
    k.currentRank && k.previousRank && k.currentRank < k.previousRank
  ).length;

  return (
    <div className="min-h-screen bg-black text-netflix-white">
      <Navigation />
      <div className="pt-24 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-4xl font-bold mb-2">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Manage your IPTV Premium platform settings and monitor performance
            </p>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5 bg-netflix-black border border-netflix-grey mb-8">
              <TabsTrigger value="content" className="data-[state=active]:bg-primary">
                <Edit3 className="w-4 h-4 mr-2" />
                Content Editor
              </TabsTrigger>
              <TabsTrigger value="code-injection" className="data-[state=active]:bg-primary">
                <Code className="w-4 h-4 mr-2" />
                Code Injection
              </TabsTrigger>
              <TabsTrigger value="contacts" className="data-[state=active]:bg-primary">
                <Mail className="w-4 h-4 mr-2" />
                Support Messages
              </TabsTrigger>
              <TabsTrigger value="analytics" className="data-[state=active]:bg-primary">
                <BarChart3 className="w-4 h-4 mr-2" />
                Analytics
              </TabsTrigger>
              <TabsTrigger value="settings" className="data-[state=active]:bg-primary">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </TabsTrigger>
            </TabsList>

            {/* Content Editor */}
            <TabsContent value="content" className="space-y-8">
              <ContentEditor />
            </TabsContent>

            {/* Analytics */}
            <TabsContent value="analytics" className="space-y-8">
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="glass-effect border-netflix-grey">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-muted-foreground text-sm">Total Contacts</p>
                        <p className="text-2xl font-bold">{totalSubmissions}</p>
                      </div>
                      <Mail className="w-8 h-8 text-primary" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-effect border-netflix-grey">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-muted-foreground text-sm">New Messages</p>
                        <p className="text-2xl font-bold">{newSubmissions}</p>
                      </div>
                      <Activity className="w-8 h-8 text-success-green" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-effect border-netflix-grey">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-muted-foreground text-sm">Tracked Keywords</p>
                        <p className="text-2xl font-bold">{totalKeywords}</p>
                      </div>
                      <Search className="w-8 h-8 text-primary" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-effect border-netflix-grey">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-muted-foreground text-sm">Improving Rankings</p>
                        <p className="text-2xl font-bold">{improvingKeywords}</p>
                      </div>
                      <TrendingUp className="w-8 h-8 text-success-green" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid lg:grid-cols-2 gap-8">
                <Card className="glass-effect border-netflix-grey">
                  <CardHeader>
                    <CardTitle>Recent Contact Submissions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {contactSubmissions.slice(0, 5).map((submission: ContactSubmission) => (
                        <div key={submission.id} className="flex items-center justify-between p-3 bg-netflix-black/50 rounded-lg">
                          <div>
                            <p className="font-medium">{submission.firstName} {submission.lastName}</p>
                            <p className="text-sm text-muted-foreground">{submission.email}</p>
                          </div>
                          <Badge variant={submission.status === 'new' ? 'default' : 'secondary'}>
                            {submission.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="glass-effect border-netflix-grey">
                  <CardHeader>
                    <CardTitle>Keyword Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {keywords.slice(0, 5).map((keyword: Keyword) => (
                        <div key={keyword.id} className="flex items-center justify-between p-3 bg-netflix-black/50 rounded-lg">
                          <div>
                            <p className="font-medium">{keyword.keyword}</p>
                            <p className="text-sm text-muted-foreground">{keyword.language}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-bold">#{keyword.currentRank}</span>
                            {keyword.previousRank && keyword.currentRank < keyword.previousRank ? (
                              <TrendingUp className="w-4 h-4 text-success-green" />
                            ) : (
                              <TrendingDown className="w-4 h-4 text-primary" />
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Code Injection Management */}
            <TabsContent value="code-injection" className="space-y-8">
              <Card className="glass-effect border-netflix-grey">
                <CardHeader>
                  <CardTitle>Add New Code Snippet</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleCreateSnippet} className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Snippet Name</Label>
                        <Input
                          id="name"
                          value={newCodeSnippet.name}
                          onChange={(e) => setNewCodeSnippet(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="e.g., Google Analytics"
                          className="bg-netflix-black border-netflix-grey"
                        />
                      </div>
                      <div>
                        <Label htmlFor="type">Injection Location</Label>
                        <Select 
                          value={newCodeSnippet.type} 
                          onValueChange={(value) => setNewCodeSnippet(prev => ({ ...prev, type: value as any }))}
                        >
                          <SelectTrigger className="bg-netflix-black border-netflix-grey">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="head">Head Section</SelectItem>
                            <SelectItem value="body">Body Section</SelectItem>
                            <SelectItem value="footer">Footer Section</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="code">Code</Label>
                      <Textarea
                        id="code"
                        value={newCodeSnippet.code}
                        onChange={(e) => setNewCodeSnippet(prev => ({ ...prev, code: e.target.value }))}
                        placeholder="Paste your tracking code here..."
                        rows={6}
                        className="bg-netflix-black border-netflix-grey font-mono text-sm"
                      />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={newCodeSnippet.enabled}
                        onCheckedChange={(enabled) => setNewCodeSnippet(prev => ({ ...prev, enabled }))}
                      />
                      <Label>Enable snippet</Label>
                    </div>
                    
                    <Button 
                      type="submit" 
                      className="bg-primary hover:bg-primary/90"
                      disabled={createSnippetMutation.isPending}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add Snippet
                    </Button>
                  </form>
                </CardContent>
              </Card>

              <Card className="glass-effect border-netflix-grey">
                <CardHeader>
                  <CardTitle>Existing Code Snippets</CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingSnippets ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                  ) : codeSnippets.length > 0 ? (
                    <div className="space-y-4">
                      {codeSnippets.map((snippet: CodeSnippet) => (
                        <div key={snippet.id} className="p-4 bg-netflix-black/50 rounded-lg">
                          <div className="flex items-center justify-between mb-3">
                            <div>
                              <h3 className="font-medium">{snippet.name}</h3>
                              <p className="text-sm text-muted-foreground">
                                {snippet.type} • Created {new Date(snippet.createdAt).toLocaleDateString()}
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Switch
                                checked={snippet.enabled}
                                onCheckedChange={(enabled) => 
                                  updateSnippetMutation.mutate({ id: snippet.id, enabled })
                                }
                              />
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => deleteSnippetMutation.mutate(snippet.id)}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                          <div className="bg-black/50 p-3 rounded border border-netflix-grey">
                            <code className="text-sm text-muted-foreground">
                              {snippet.code.substring(0, 200)}
                              {snippet.code.length > 200 && '...'}
                            </code>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Code className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No code snippets created yet</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Contact Management */}
            <TabsContent value="contacts" className="space-y-8">
              <Card className="glass-effect border-netflix-grey">
                <CardHeader>
                  <CardTitle>Contact Submissions</CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingContacts ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                  ) : contactSubmissions.length > 0 ? (
                    <div className="space-y-4">
                      {contactSubmissions.map((submission: ContactSubmission) => (
                        <div key={submission.id} className="p-4 bg-netflix-black/50 rounded-lg">
                          <div className="flex items-start justify-between mb-3">
                            <div>
                              <h3 className="font-medium">
                                {submission.firstName} {submission.lastName}
                              </h3>
                              <p className="text-sm text-muted-foreground">{submission.email}</p>
                              {submission.company && (
                                <p className="text-sm text-muted-foreground">{submission.company}</p>
                              )}
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge variant={submission.status === 'new' ? 'default' : 'secondary'}>
                                {submission.status}
                              </Badge>
                              <Select
                                value={submission.status}
                                onValueChange={(status) => 
                                  updateContactStatusMutation.mutate({ id: submission.id, status })
                                }
                              >
                                <SelectTrigger className="w-32 bg-netflix-black border-netflix-grey">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="new">New</SelectItem>
                                  <SelectItem value="read">Read</SelectItem>
                                  <SelectItem value="replied">Replied</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <div className="bg-black/50 p-3 rounded border border-netflix-grey">
                            <p className="text-sm">{submission.message}</p>
                          </div>
                          <p className="text-xs text-muted-foreground mt-2">
                            Submitted {new Date(submission.createdAt).toLocaleString()} • Language: {submission.language}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Mail className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No contact submissions yet</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* SEO Management */}
            <TabsContent value="seo" className="space-y-8">
              <Card className="glass-effect border-netflix-grey">
                <CardHeader>
                  <CardTitle>Keyword Rankings</CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingKeywords ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                  ) : keywords.length > 0 ? (
                    <div className="space-y-4">
                      {keywords.map((keyword: Keyword) => (
                        <div key={keyword.id} className="p-4 bg-netflix-black/50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-medium">{keyword.keyword}</h3>
                              <p className="text-sm text-muted-foreground">
                                Language: {keyword.language} • URL: {keyword.url}
                              </p>
                            </div>
                            <div className="flex items-center space-x-4">
                              <div className="text-center">
                                <p className="text-2xl font-bold">#{keyword.currentRank}</p>
                                <p className="text-xs text-muted-foreground">Current</p>
                              </div>
                              {keyword.previousRank && (
                                <>
                                  <div className="text-center">
                                    <p className="text-lg text-muted-foreground">#{keyword.previousRank}</p>
                                    <p className="text-xs text-muted-foreground">Previous</p>
                                  </div>
                                  <div>
                                    {keyword.currentRank < keyword.previousRank ? (
                                      <TrendingUp className="w-6 h-6 text-success-green" />
                                    ) : keyword.currentRank > keyword.previousRank ? (
                                      <TrendingDown className="w-6 h-6 text-primary" />
                                    ) : (
                                      <div className="w-6 h-6 bg-muted-foreground rounded-full"></div>
                                    )}
                                  </div>
                                </>
                              )}
                            </div>
                          </div>
                          {keyword.lastChecked && (
                            <p className="text-xs text-muted-foreground mt-2">
                              Last checked: {new Date(keyword.lastChecked).toLocaleString()}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No keywords being tracked yet</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings */}
            <TabsContent value="settings" className="space-y-8">
              <Card className="glass-effect border-netflix-grey">
                <CardHeader>
                  <CardTitle>Platform Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="font-medium mb-3">Language Settings</h3>
                    <p className="text-sm text-muted-foreground mb-3">
                      Manage supported languages and translation settings
                    </p>
                    <Button variant="outline" className="border-netflix-grey">
                      <Languages className="w-4 h-4 mr-2" />
                      Manage Languages
                    </Button>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">SEO Configuration</h3>
                    <p className="text-sm text-muted-foreground mb-3">
                      Configure Google Search Console and keyword tracking settings
                    </p>
                    <Button variant="outline" className="border-netflix-grey">
                      <Search className="w-4 h-4 mr-2" />
                      Configure SEO
                    </Button>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Telegram Integration</h3>
                    <p className="text-sm text-muted-foreground mb-3">
                      Set up Telegram bot for real-time notifications
                    </p>
                    <Button variant="outline" className="border-netflix-grey">
                      <Activity className="w-4 h-4 mr-2" />
                      Configure Telegram
                    </Button>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Analytics</h3>
                    <p className="text-sm text-muted-foreground mb-3">
                      Configure Google Analytics and tracking settings
                    </p>
                    <Button variant="outline" className="border-netflix-grey">
                      <BarChart3 className="w-4 h-4 mr-2" />
                      Configure Analytics
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
      <Footer />
    </div>
  );
}
