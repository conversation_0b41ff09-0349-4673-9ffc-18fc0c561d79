import type { IStorage, User, Translation, CodeSnippet, Keyword, ContactSubmission, Analytics, Documentation } from "./storage";

// Mock data for demo mode
const mockTranslations: Record<string, string> = {
  'hero.title': 'Premium IPTV Service - 4K Streaming',
  'hero.subtitle': 'Watch 10,000+ channels, sports, movies & TV shows in Ultra HD quality',
  'hero.cta': 'Start Free Trial',
  'nav.home': 'Home',
  'nav.channels': 'Channels',
  'nav.sports': 'Sports',
  'nav.movies': 'Movies',
  'nav.pricing': 'Pricing',
  'nav.support': 'Support',
  'nav.contact': 'Contact',
  'features.title': 'Why Choose Our IPTV Service?',
  'features.hd_quality': '4K Ultra HD Quality',
  'features.channels': '10,000+ Channels',
  'features.sports': 'Live Sports',
  'features.support': '24/7 Support',
  'pricing.title': 'Choose Your Plan',
  'pricing.basic': 'Basic Plan',
  'pricing.premium': 'Premium Plan',
  'pricing.ultimate': 'Ultimate Plan',
  'contact.title': 'Contact Us',
  'contact.name': 'Full Name',
  'contact.email': 'Email Address',
  'contact.message': 'Message',
  'contact.send': 'Send Message',
};

export class DemoStorage implements IStorage {
  // Users
  async getUser(id: number): Promise<User | undefined> {
    if (id === 1) {
      return {
        id: 1,
        username: 'admin',
        password: 'admin123',
        email: '<EMAIL>',
        role: 'admin',
        createdAt: new Date()
      };
    }
    return undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    if (username === 'admin') {
      return this.getUser(1);
    }
    return undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    if (email === '<EMAIL>') {
      return this.getUser(1);
    }
    return undefined;
  }

  async createUser(user: any): Promise<User> {
    return {
      id: Date.now(),
      ...user,
      createdAt: new Date()
    };
  }

  // Translations
  async getTranslations(language: string, section?: string): Promise<Translation[]> {
    return Object.entries(mockTranslations).map(([key, value], index) => ({
      id: index + 1,
      key,
      language,
      value,
      section: key.split('.')[0],
      createdAt: new Date(),
      updatedAt: new Date()
    }));
  }

  async getTranslation(key: string, language: string): Promise<Translation | undefined> {
    const value = mockTranslations[key];
    if (value) {
      return {
        id: 1,
        key,
        language,
        value,
        section: key.split('.')[0],
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }
    return undefined;
  }

  async createTranslation(translation: any): Promise<Translation> {
    return {
      id: Date.now(),
      ...translation,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  async updateTranslation(id: number, translation: any): Promise<Translation> {
    return {
      id,
      ...translation,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  // Code Snippets
  async getCodeSnippets(): Promise<CodeSnippet[]> {
    return [
      {
        id: 1,
        name: 'Google Analytics',
        type: 'head',
        code: '<!-- Google Analytics Demo -->',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  async getCodeSnippetsByType(type: string): Promise<CodeSnippet[]> {
    return this.getCodeSnippets();
  }

  async createCodeSnippet(snippet: any): Promise<CodeSnippet> {
    return {
      id: Date.now(),
      ...snippet,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  async updateCodeSnippet(id: number, snippet: any): Promise<CodeSnippet> {
    return {
      id,
      ...snippet,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  async deleteCodeSnippet(id: number): Promise<void> {
    // Demo mode - do nothing
  }

  // Keywords
  async getKeywords(language?: string): Promise<Keyword[]> {
    return [
      {
        id: 1,
        keyword: 'IPTV Premium',
        language: 'en',
        currentRank: 15,
        previousRank: 20,
        url: 'https://iptv-premium.com',
        lastChecked: new Date(),
        createdAt: new Date()
      }
    ];
  }

  async createKeyword(keyword: any): Promise<Keyword> {
    return {
      id: Date.now(),
      ...keyword,
      createdAt: new Date()
    };
  }

  async updateKeyword(id: number, keyword: any): Promise<Keyword> {
    return {
      id,
      ...keyword,
      createdAt: new Date()
    };
  }

  // Contact Submissions
  async getContactSubmissions(): Promise<ContactSubmission[]> {
    return [
      {
        id: 1,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        company: 'Demo Company',
        message: 'This is a demo contact submission',
        language: 'en',
        status: 'new',
        createdAt: new Date()
      }
    ];
  }

  async createContactSubmission(submission: any): Promise<ContactSubmission> {
    return {
      id: Date.now(),
      ...submission,
      status: 'new',
      createdAt: new Date()
    };
  }

  async updateContactSubmissionStatus(id: number, status: string): Promise<ContactSubmission> {
    return {
      id,
      firstName: 'Demo',
      lastName: 'User',
      email: '<EMAIL>',
      company: null,
      message: 'Demo message',
      language: 'en',
      status,
      createdAt: new Date()
    };
  }

  // Analytics
  async getAnalytics(language?: string): Promise<Analytics[]> {
    return [
      {
        id: 1,
        date: new Date(),
        language: 'en',
        pageViews: 1250,
        uniqueVisitors: 890,
        conversions: 45,
        bounceRate: 35,
        avgSessionDuration: 180
      }
    ];
  }

  async createAnalytics(analytics: any): Promise<Analytics> {
    return {
      id: Date.now(),
      ...analytics
    };
  }

  // Documentation
  async getDocumentation(language: string, category?: string): Promise<Documentation[]> {
    return [
      {
        id: 1,
        title: 'How to Install IPTV on Android',
        slug: 'install-android',
        content: '# Android Installation\n\nStep-by-step guide for Android devices...',
        category: 'android',
        language,
        difficulty: 'beginner',
        estimatedTime: 15,
        published: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  async getDocumentationBySlug(slug: string, language: string): Promise<Documentation | undefined> {
    const docs = await this.getDocumentation(language);
    return docs.find(doc => doc.slug === slug);
  }

  async createDocumentation(doc: any): Promise<Documentation> {
    return {
      id: Date.now(),
      ...doc,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  async updateDocumentation(id: number, doc: any): Promise<Documentation> {
    return {
      id,
      ...doc,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
}
