import { Navigation } from '@/components/navigation';
import { Footer } from '@/components/footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSEO } from '@/hooks/use-seo';
import { useLanguage } from '@/hooks/use-language';

export default function Terms() {
  const { t, currentLanguage } = useLanguage();

  useSEO({
    title: 'Terms of Service - IPTV Premium',
    description: 'IPTV Premium terms of service outlining the rules and regulations for using our multi-language subscription management platform.',
    keywords: 'IPTV Premium terms of service, user agreement, platform rules, subscription terms',
    structuredData: {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: 'Terms of Service',
      description: 'Terms of service for IPTV Premium platform',
      publisher: {
        '@type': 'Organization',
        name: 'IPTV Premium',
      },
    },
  });

  return (
    <div className="min-h-screen bg-black text-netflix-white">
      <Navigation />
      <div className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">Terms of Service</h1>
            <p className="text-xl text-muted-foreground">
              Last updated: {new Date().toLocaleDateString()}
            </p>
          </div>

          <div className="space-y-8">
            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle>1. Acceptance of Terms</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-invert max-w-none">
                <p className="text-muted-foreground">
                  By accessing and using IPTV Premium services, you accept and agree to be bound by the 
                  terms and provision of this agreement. If you do not agree to abide by the above, 
                  please do not use this service.
                </p>
              </CardContent>
            </Card>

            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle>2. Service Description</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-invert max-w-none">
                <p className="text-muted-foreground">
                  IPTV Premium provides a comprehensive subscription management platform with multi-language 
                  support, SEO optimization tools, and analytics capabilities for IPTV service providers.
                </p>
                <ul className="text-muted-foreground mt-4 space-y-2">
                  <li>• Multi-language platform supporting 15 languages</li>
                  <li>• Advanced SEO optimization and keyword tracking</li>
                  <li>• Comprehensive analytics and reporting tools</li>
                  <li>• Custom code injection for tracking and analytics</li>
                  <li>• Real-time notifications via Telegram integration</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle>3. User Accounts and Registration</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-invert max-w-none">
                <p className="text-muted-foreground">
                  To access certain features of our service, you must register for an account. 
                  You are responsible for maintaining the confidentiality of your account credentials.
                </p>
                <ul className="text-muted-foreground mt-4 space-y-2">
                  <li>• Provide accurate and complete registration information</li>
                  <li>• Maintain the security of your account credentials</li>
                  <li>• Notify us immediately of any unauthorized use</li>
                  <li>• Accept responsibility for all activities under your account</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle>4. Subscription and Payment Terms</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-invert max-w-none">
                <p className="text-muted-foreground">
                  Our services are provided on a subscription basis with different pricing tiers. 
                  Payment is due in advance for each subscription period.
                </p>
                <ul className="text-muted-foreground mt-4 space-y-2">
                  <li>• Subscriptions are billed monthly or annually</li>
                  <li>• Payment must be received before service activation</li>
                  <li>• Refunds are provided according to our refund policy</li>
                  <li>• Price changes will be communicated in advance</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle>5. Acceptable Use Policy</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-invert max-w-none">
                <p className="text-muted-foreground">
                  You agree to use our services only for lawful purposes and in accordance with 
                  these Terms of Service.
                </p>
                <ul className="text-muted-foreground mt-4 space-y-2">
                  <li>• Do not use the service for illegal activities</li>
                  <li>• Do not attempt to breach security measures</li>
                  <li>• Do not interfere with other users' access to the service</li>
                  <li>• Do not violate intellectual property rights</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle>6. Intellectual Property Rights</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-invert max-w-none">
                <p className="text-muted-foreground">
                  All content, features, and functionality of our service are owned by IPTV Premium 
                  and are protected by international copyright, trademark, and other intellectual property laws.
                </p>
              </CardContent>
            </Card>

            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle>7. Limitation of Liability</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-invert max-w-none">
                <p className="text-muted-foreground">
                  IPTV Premium shall not be liable for any indirect, incidental, special, consequential, 
                  or punitive damages resulting from your use of or inability to use the service.
                </p>
              </CardContent>
            </Card>

            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle>8. Termination</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-invert max-w-none">
                <p className="text-muted-foreground">
                  We reserve the right to terminate or suspend your account and access to our services 
                  at our sole discretion, without notice, for conduct that we believe violates these Terms.
                </p>
              </CardContent>
            </Card>

            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle>9. Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-invert max-w-none">
                <p className="text-muted-foreground">
                  Questions about the Terms of Service should be sent to us at:
                </p>
                <div className="mt-4 text-muted-foreground">
                  <p>Email: <EMAIL></p>
                  <p>Telegram: @IPTVPremiumSupport</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
