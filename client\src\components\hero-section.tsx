import { Button } from '@/components/ui/button';
import { useLanguage } from '@/hooks/use-language';
import { Play, Tv, Shield, Globe, Zap, Star } from 'lucide-react';

export function HeroSection() {
  const { t } = useLanguage();

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="hero-gradient min-h-screen flex items-center pt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8 fade-in">
            <div className="space-y-4">
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <span className="text-sm text-muted-foreground">500,000+ satisfied customers</span>
              </div>
              
              <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                Premium <span className="text-primary">IPTV</span>
                <br />Streaming Experience
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Watch 15,000+ premium channels, live sports, movies and TV shows in stunning 4K quality. 
                All your favorite content from around the world in one subscription.
              </p>
            </div>
            
            {/* Trust Signals */}
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Zap className="w-5 h-5 text-success-green mr-2" />
                <span>No Buffering</span>
              </div>
              <div className="flex items-center">
                <Globe className="w-5 h-5 text-primary mr-2" />
                <span>Global Content</span>
              </div>
              <div className="flex items-center">
                <Shield className="w-5 h-5 text-success-green mr-2" />
                <span>99.9% Uptime</span>
              </div>
            </div>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary/90 text-primary-foreground pulse-glow px-8 py-4"
                onClick={() => scrollToSection('pricing')}
              >
                <Tv className="w-5 h-5 mr-2" />
                Start Watching Now
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                className="border-netflix-grey hover:border-primary px-8 py-4"
                onClick={() => scrollToSection('features')}
              >
                <Play className="w-5 h-5 mr-2" />
                View Channel List
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-4 gap-4 pt-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">15K+</div>
                <div className="text-xs text-muted-foreground">Channels</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">50K+</div>
                <div className="text-xs text-muted-foreground">Movies</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">4K</div>
                <div className="text-xs text-muted-foreground">Quality</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">24/7</div>
                <div className="text-xs text-muted-foreground">Support</div>
              </div>
            </div>
          </div>
          
          <div className="relative">
            <img 
              src="https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=1200&h=800&auto=format&fit=crop" 
              alt="Football stadium with IPTV premium sports streaming" 
              className="rounded-2xl shadow-2xl w-full h-auto fade-in" 
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-2xl"></div>
            
            {/* Overlay content on image */}
            <div className="absolute bottom-6 left-6 right-6">
              <div className="bg-black/80 backdrop-blur-sm rounded-lg p-4">
                <h3 className="text-white font-semibold mb-2">Live Sports & Premium Content</h3>
                <div className="flex items-center space-x-4 text-sm text-gray-300">
                  <span>🏈 All Sports</span>
                  <span>🎬 Latest Movies</span>
                  <span>📺 Live TV</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
