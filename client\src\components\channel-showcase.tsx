import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Play, Users, Star } from "lucide-react";

export function ChannelShowcase() {
  const featuredChannels = [
    {
      id: 1,
      name: "Premium Sports HD",
      category: "Sports",
      image: "https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=600&h=400&auto=format&fit=crop",
      description: "All Premier League, Champions League, NFL, NBA and more",
      viewers: "2.5M",
      rating: 4.9,
      live: true
    },
    {
      id: 2,
      name: "Hollywood Premium",
      category: "Movies",
      image: "https://images.unsplash.com/photo-1489599516861-2c92f2a95a56?q=80&w=600&h=400&auto=format&fit=crop",
      description: "Latest blockbusters and classic movies in 4K",
      viewers: "1.8M",
      rating: 4.8,
      live: false
    },
    {
      id: 3,
      name: "Formula 1 Live",
      category: "Sports",
      image: "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?q=80&w=600&h=400&auto=format&fit=crop",
      description: "Every F1 race, qualifying and practice session",
      viewers: "950K",
      rating: 4.9,
      live: true
    },
    {
      id: 4,
      name: "International Cinema",
      category: "Movies",
      image: "https://images.unsplash.com/photo-1478720568477-b0b8b2c38030?q=80&w=600&h=400&auto=format&fit=crop",
      description: "World cinema from Europe, Asia and beyond",
      viewers: "720K",
      rating: 4.7,
      live: false
    },
    {
      id: 5,
      name: "UFC Fight Pass",
      category: "Sports",
      image: "https://images.unsplash.com/photo-1544737151-6e4b9d5b3b5a?q=80&w=600&h=400&auto=format&fit=crop",
      description: "All UFC events, prelims and exclusive content",
      viewers: "1.2M",
      rating: 4.8,
      live: true
    },
    {
      id: 6,
      name: "Netflix Originals",
      category: "Series",
      image: "https://images.unsplash.com/photo-1522869635100-9f4c5e86aa37?q=80&w=600&h=400&auto=format&fit=crop",
      description: "Exclusive series and documentaries",
      viewers: "3.1M",
      rating: 4.9,
      live: false
    }
  ];

  return (
    <section className="py-24 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Premium <span className="text-primary">IPTV Channels</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Experience the world's best content with our premium channel lineup. 
            From live sports to latest movies, we have everything you love.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredChannels.map((channel) => (
            <Card key={channel.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden border-border/50 hover:border-primary/50">
              <div className="relative">
                <img 
                  src={channel.image}
                  alt={channel.name}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                
                {/* Live indicator */}
                {channel.live && (
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-red-600 text-white">
                      <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse" />
                      LIVE
                    </Badge>
                  </div>
                )}

                {/* Category badge */}
                <div className="absolute top-4 right-4">
                  <Badge variant="secondary" className="bg-black/50 text-white">
                    {channel.category}
                  </Badge>
                </div>

                {/* Play button overlay */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-primary rounded-full p-4 transform scale-75 group-hover:scale-100 transition-transform duration-300">
                    <Play className="w-8 h-8 text-white" />
                  </div>
                </div>

                {/* Channel info overlay */}
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <h3 className="font-bold text-lg mb-1">{channel.name}</h3>
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center">
                      <Users className="w-4 h-4 mr-1" />
                      {channel.viewers}
                    </div>
                    <div className="flex items-center">
                      <Star className="w-4 h-4 mr-1 fill-yellow-400 text-yellow-400" />
                      {channel.rating}
                    </div>
                  </div>
                </div>
              </div>

              <CardContent className="p-6">
                <p className="text-muted-foreground">{channel.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Popular categories */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold mb-8">Popular Categories</h3>
          <div className="flex flex-wrap justify-center gap-4">
            {[
              { name: "🏈 Sports", count: "2,500+ channels" },
              { name: "🎬 Movies", count: "50,000+ titles" },
              { name: "📺 TV Shows", count: "15,000+ series" },
              { name: "📰 News", count: "500+ channels" },
              { name: "🎵 Music", count: "300+ channels" },
              { name: "👶 Kids", count: "200+ channels" }
            ].map((category, index) => (
              <div key={index} className="bg-card border rounded-lg p-4 hover:border-primary transition-colors">
                <div className="font-semibold">{category.name}</div>
                <div className="text-sm text-muted-foreground">{category.count}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}