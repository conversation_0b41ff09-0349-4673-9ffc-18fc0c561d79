import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Save, Plus, Trash2, Eye } from "lucide-react";

export function ContentEditor() {
  const [heroContent, setHeroContent] = useState({
    title: "Premium IPTV Streaming Experience",
    subtitle: "Watch 15,000+ premium channels, live sports, movies and TV shows in stunning 4K quality. All your favorite content from around the world in one subscription.",
    trustBadge: "500,000+ satisfied customers",
    backgroundImage: "https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=1200&h=800&auto=format&fit=crop"
  });

  const [pricingPlans, setPricingPlans] = useState([
    {
      id: 'basic',
      name: 'Basic IPTV',
      price: 15,
      period: 'month',
      features: ['5,000+ Premium Channels', 'HD Quality Streaming', 'Sports & Movies', '2 Device Connections', 'Email Support'],
      popular: false
    },
    {
      id: 'premium',
      name: 'Premium IPTV',
      price: 25,
      period: 'month',
      features: ['10,000+ Premium Channels', '4K & HD Quality', 'All Sports & PPV Events', '5 Device Connections', 'VOD Library (50,000+ Movies)', '24/7 Priority Support'],
      popular: true
    },
    {
      id: 'ultimate',
      name: 'Ultimate IPTV',
      price: 35,
      period: 'month',
      features: ['15,000+ Premium Channels', '4K Ultra HD Quality', 'All Sports, Movies & Shows', 'Unlimited Device Connections', 'Complete VOD Library', 'Instant 24/7 Support', 'Free Installation Setup'],
      popular: false
    }
  ]);

  const [channels, setChannels] = useState([
    {
      id: 1,
      name: "Premium Sports HD",
      category: "Sports",
      image: "https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=600&h=400&auto=format&fit=crop",
      description: "All Premier League, Champions League, NFL, NBA and more",
      viewers: "2.5M",
      rating: 4.9,
      live: true
    },
    {
      id: 2,
      name: "Hollywood Premium",
      category: "Movies",
      image: "https://images.unsplash.com/photo-1489599516861-2c92f2a95a56?q=80&w=600&h=400&auto=format&fit=crop",
      description: "Latest blockbusters and classic movies in 4K",
      viewers: "1.8M",
      rating: 4.8,
      live: false
    }
  ]);

  const saveContent = () => {
    // Save to localStorage for demo purposes
    localStorage.setItem('iptvContent', JSON.stringify({
      hero: heroContent,
      pricing: pricingPlans,
      channels: channels
    }));
    alert('Content saved successfully!');
  };

  const addChannel = () => {
    const newChannel = {
      id: Date.now(),
      name: "New Channel",
      category: "Entertainment",
      image: "https://images.unsplash.com/photo-1522869635100-9f4c5e86aa37?q=80&w=600&h=400&auto=format&fit=crop",
      description: "Description of the new channel",
      viewers: "0",
      rating: 4.5,
      live: false
    };
    setChannels([...channels, newChannel]);
  };

  const removeChannel = (id: number) => {
    setChannels(channels.filter(c => c.id !== id));
  };

  const updateChannel = (id: number, field: string, value: any) => {
    setChannels(channels.map(c => 
      c.id === id ? { ...c, [field]: value } : c
    ));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold">Content Management</h2>
        <Button onClick={saveContent} className="bg-green-600 hover:bg-green-700">
          <Save className="w-4 h-4 mr-2" />
          Save All Changes
        </Button>
      </div>

      <Tabs defaultValue="hero" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="hero">Hero Section</TabsTrigger>
          <TabsTrigger value="pricing">Pricing Plans</TabsTrigger>
          <TabsTrigger value="channels">Channels</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="hero" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Hero Section Content</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Main Title</Label>
                <Input
                  id="title"
                  value={heroContent.title}
                  onChange={(e) => setHeroContent({...heroContent, title: e.target.value})}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="subtitle">Subtitle</Label>
                <Textarea
                  id="subtitle"
                  value={heroContent.subtitle}
                  onChange={(e) => setHeroContent({...heroContent, subtitle: e.target.value})}
                  className="mt-1"
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="trustBadge">Trust Badge Text</Label>
                <Input
                  id="trustBadge"
                  value={heroContent.trustBadge}
                  onChange={(e) => setHeroContent({...heroContent, trustBadge: e.target.value})}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="backgroundImage">Background Image URL</Label>
                <Input
                  id="backgroundImage"
                  value={heroContent.backgroundImage}
                  onChange={(e) => setHeroContent({...heroContent, backgroundImage: e.target.value})}
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pricing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pricing Plans</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-3">
                {pricingPlans.map((plan, index) => (
                  <div key={plan.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <Input
                        value={plan.name}
                        onChange={(e) => {
                          const updated = [...pricingPlans];
                          updated[index].name = e.target.value;
                          setPricingPlans(updated);
                        }}
                        className="font-semibold"
                      />
                      {plan.popular && <Badge className="bg-red-600">Popular</Badge>}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span>$</span>
                      <Input
                        type="number"
                        value={plan.price}
                        onChange={(e) => {
                          const updated = [...pricingPlans];
                          updated[index].price = parseInt(e.target.value);
                          setPricingPlans(updated);
                        }}
                        className="w-20"
                      />
                      <span>/{plan.period}</span>
                    </div>
                    <div className="space-y-2">
                      {plan.features.map((feature, featureIndex) => (
                        <Input
                          key={featureIndex}
                          value={feature}
                          onChange={(e) => {
                            const updated = [...pricingPlans];
                            updated[index].features[featureIndex] = e.target.value;
                            setPricingPlans(updated);
                          }}
                          className="text-sm"
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="channels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Featured Channels
                <Button onClick={addChannel} size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Channel
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                {channels.map((channel) => (
                  <div key={channel.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <Input
                        value={channel.name}
                        onChange={(e) => updateChannel(channel.id, 'name', e.target.value)}
                        className="font-semibold"
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => removeChannel(channel.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                    <Input
                      value={channel.category}
                      onChange={(e) => updateChannel(channel.id, 'category', e.target.value)}
                      placeholder="Category"
                    />
                    <Textarea
                      value={channel.description}
                      onChange={(e) => updateChannel(channel.id, 'description', e.target.value)}
                      placeholder="Description"
                      rows={2}
                    />
                    <Input
                      value={channel.image}
                      onChange={(e) => updateChannel(channel.id, 'image', e.target.value)}
                      placeholder="Image URL"
                    />
                    <div className="flex space-x-2">
                      <Input
                        value={channel.viewers}
                        onChange={(e) => updateChannel(channel.id, 'viewers', e.target.value)}
                        placeholder="Viewers"
                        className="w-24"
                      />
                      <Input
                        type="number"
                        step="0.1"
                        value={channel.rating}
                        onChange={(e) => updateChannel(channel.id, 'rating', parseFloat(e.target.value))}
                        placeholder="Rating"
                        className="w-20"
                      />
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={channel.live}
                          onChange={(e) => updateChannel(channel.id, 'live', e.target.checked)}
                        />
                        <span>Live</span>
                      </label>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="w-5 h-5 mr-2" />
                Content Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Hero Preview */}
                <div className="border rounded-lg p-6 bg-gradient-to-r from-gray-900 to-black text-white">
                  <h1 className="text-4xl font-bold mb-4">{heroContent.title}</h1>
                  <p className="text-xl mb-4">{heroContent.subtitle}</p>
                  <Badge>{heroContent.trustBadge}</Badge>
                </div>

                {/* Pricing Preview */}
                <div className="grid md:grid-cols-3 gap-4">
                  {pricingPlans.map((plan) => (
                    <div key={plan.id} className="border rounded-lg p-4 text-center">
                      <h3 className="font-bold text-lg">{plan.name}</h3>
                      <div className="text-2xl font-bold my-2">${plan.price}/{plan.period}</div>
                      <ul className="text-sm space-y-1">
                        {plan.features.slice(0, 3).map((feature, index) => (
                          <li key={index}>✓ {feature}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>

                {/* Channels Preview */}
                <div className="grid md:grid-cols-2 gap-4">
                  {channels.slice(0, 4).map((channel) => (
                    <div key={channel.id} className="border rounded-lg overflow-hidden">
                      <img src={channel.image} alt={channel.name} className="w-full h-32 object-cover" />
                      <div className="p-3">
                        <h4 className="font-semibold">{channel.name}</h4>
                        <p className="text-sm text-gray-600">{channel.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}