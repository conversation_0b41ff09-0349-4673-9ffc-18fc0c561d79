@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 0%; /* #000000 */
  --foreground: 0 0% 100%; /* #FFFFFF */
  --muted: 0 0% 8.2%; /* #141414 */
  --muted-foreground: 0 0% 33.9%; /* #564D4D */
  --popover: 0 0% 8.2%; /* #141414 */
  --popover-foreground: 0 0% 100%; /* #FFFFFF */
  --card: 0 0% 8.2%; /* #141414 */
  --card-foreground: 0 0% 100%; /* #FFFFFF */
  --border: 0 0% 33.9%; /* #564D4D */
  --input: 0 0% 33.9%; /* #564D4D */
  --primary: 348 89% 47%; /* #E50914 */
  --primary-foreground: 0 0% 100%; /* #FFFFFF */
  --secondary: 0 0% 8.2%; /* #141414 */
  --secondary-foreground: 0 0% 100%; /* #FFFFFF */
  --accent: 0 0% 8.2%; /* #141414 */
  --accent-foreground: 0 0% 100%; /* #FFFFFF */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 100%;
  --ring: 348 89% 47%; /* #E50914 */
  --radius: 0.75rem;
  --chart-1: 348 89% 47%; /* #E50914 */
  --chart-2: 0 0% 33.9%; /* #564D4D */
  --chart-3: 0 0% 100%; /* #FFFFFF */
  --chart-4: 120 61% 50%; /* Success Green */
  --chart-5: 0 0% 8.2%; /* #141414 */
  
  /* Netflix specific colors */
  --netflix-red: 348 89% 47%; /* #E50914 */
  --netflix-black: 0 0% 8.2%; /* #141414 */
  --netflix-grey: 0 0% 33.9%; /* #564D4D */
  --netflix-white: 0 0% 100%; /* #FFFFFF */
  --success-green: 120 61% 50%; /* #46D369 */
}

.dark {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;
  --muted: 0 0% 8.2%;
  --muted-foreground: 0 0% 33.9%;
  --popover: 0 0% 8.2%;
  --popover-foreground: 0 0% 100%;
  --card: 0 0% 8.2%;
  --card-foreground: 0 0% 100%;
  --border: 0 0% 33.9%;
  --input: 0 0% 33.9%;
  --primary: 348 89% 47%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 8.2%;
  --secondary-foreground: 0 0% 100%;
  --accent: 0 0% 8.2%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 100%;
  --ring: 348 89% 47%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', 'Helvetica Neue', 'Arial', sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }
}

@layer components {
  .glass-effect {
    background: rgba(20, 20, 20, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .hero-gradient {
    background: linear-gradient(135deg, #000000 0%, #141414 50%, #1a1a1a 100%);
  }
  
  .card-hover {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .card-hover:hover {
    @apply -translate-y-2;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  }
  
  .pulse-glow {
    animation: pulse-glow 2s infinite;
  }
  
  .netflix-red {
    color: hsl(var(--netflix-red));
  }
  
  .bg-netflix-red {
    background-color: hsl(var(--netflix-red));
  }
  
  .netflix-black {
    color: hsl(var(--netflix-black));
  }
  
  .bg-netflix-black {
    background-color: hsl(var(--netflix-black));
  }
  
  .netflix-grey {
    color: hsl(var(--netflix-grey));
  }
  
  .bg-netflix-grey {
    background-color: hsl(var(--netflix-grey));
  }
  
  .success-green {
    color: hsl(var(--success-green));
  }
  
  .bg-success-green {
    background-color: hsl(var(--success-green));
  }
}

@layer utilities {
  .scroll-smooth {
    scroll-behavior: smooth;
  }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(229, 9, 20, 0.3); 
  }
  50% { 
    box-shadow: 0 0 30px rgba(229, 9, 20, 0.6); 
  }
}

/* Loading animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--netflix-red));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--netflix-red) / 0.8);
}
