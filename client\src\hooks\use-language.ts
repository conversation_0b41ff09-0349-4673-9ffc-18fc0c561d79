import { useState, useEffect } from 'react';
import { i18n } from '@/lib/i18n';
import { DEFAULT_LANGUAGE } from '@/lib/constants';

export function useLanguage() {
  const [currentLanguage, setCurrentLanguage] = useState(DEFAULT_LANGUAGE);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initializeLanguage = async () => {
      const savedLanguage = localStorage.getItem('language') || DEFAULT_LANGUAGE;
      await i18n.setLanguage(savedLanguage);
      setCurrentLanguage(savedLanguage);
      setIsLoading(false);
    };

    initializeLanguage();
  }, []);

  const changeLanguage = async (language: string) => {
    setIsLoading(true);
    try {
      await i18n.setLanguage(language);
      setCurrentLanguage(language);
      
      // Trigger a page reload to update all content
      window.location.reload();
    } catch (error) {
      console.error('Failed to change language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const t = (key: string, params?: Record<string, string>) => {
    return i18n.translate(key, params);
  };

  return {
    currentLanguage,
    changeLanguage,
    isLoading,
    t,
    supportedLanguages: i18n.getSupportedLanguages(),
  };
}
