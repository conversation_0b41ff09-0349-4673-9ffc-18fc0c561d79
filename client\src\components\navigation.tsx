import { Link, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { LanguageSelector } from './language-selector';
import { useLanguage } from '@/hooks/use-language';
import { useState } from 'react';
import { Menu, X, Tv } from 'lucide-react';

export function Navigation() {
  const { t } = useLanguage();
  const [location] = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    if (location !== '/') {
      window.location.href = `/#${sectionId}`;
      return;
    }
    
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false);
  };

  return (
    <nav className="fixed top-0 w-full z-50 glass-effect">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <Tv className="h-8 w-8 text-primary" />
            <span className="text-2xl font-bold text-primary">IPTV Premium</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <button 
              onClick={() => scrollToSection('features')}
              className="text-netflix-white hover:text-primary transition-colors"
            >
              Channels
            </button>
            <button 
              onClick={() => scrollToSection('pricing')}
              className="text-netflix-white hover:text-primary transition-colors"
            >
              Pricing
            </button>
            <Link href="/documentation" className="text-netflix-white hover:text-primary transition-colors">
              Setup Guide
            </Link>
            <Link href="/contact" className="text-netflix-white hover:text-primary transition-colors">
              Support
            </Link>
          </div>

          {/* Language Selector & Admin */}
          <div className="hidden md:flex items-center space-x-4">
            <LanguageSelector />
            <Link href="/admin">
              <Button variant="default" size="sm" className="bg-primary hover:bg-primary/90">
                <i className="fas fa-cog mr-2"></i>
                Admin Panel
              </Button>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-netflix-grey">
            <div className="flex flex-col space-y-4">
              <button 
                onClick={() => scrollToSection('features')}
                className="text-netflix-white hover:text-primary transition-colors text-left"
              >
                Channels
              </button>
              <button 
                onClick={() => scrollToSection('pricing')}
                className="text-netflix-white hover:text-primary transition-colors text-left"
              >
                Pricing
              </button>
              <Link href="/documentation" className="text-netflix-white hover:text-primary transition-colors">
                Setup Guide
              </Link>
              <Link href="/contact" className="text-netflix-white hover:text-primary transition-colors">
                Support
              </Link>
              <div className="pt-4 border-t border-netflix-grey">
                <LanguageSelector />
              </div>
              <Link href="/admin">
                <Button variant="default" size="sm" className="bg-primary hover:bg-primary/90 w-full mt-4">
                  <i className="fas fa-cog mr-2"></i>
                  Admin Panel
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
