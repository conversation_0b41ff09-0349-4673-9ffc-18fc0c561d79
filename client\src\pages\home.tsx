import { Navigation } from '@/components/navigation';
import { HeroSection } from '@/components/hero-section';
import { ChannelShowcase } from '@/components/channel-showcase';
import { SportsShowcase } from '@/components/sports-showcase';
import { FeaturesSection } from '@/components/features-section';
import { PricingSection } from '@/components/pricing-section';
import { DocumentationSection } from '@/components/documentation-section';
import { TestimonialsSection } from '@/components/testimonials-section';
import { ContactSection } from '@/components/contact-section';
import { Footer } from '@/components/footer';
import { useSEO } from '@/hooks/use-seo';
import { useLanguage } from '@/hooks/use-language';

export default function Home() {
  const { currentLanguage } = useLanguage();

  useSEO({
    title: 'IPTV Premium - 15,000+ Channels, 4K Sports & Movies Streaming',
    description: 'Watch premium IPTV with 15,000+ live channels, sports, movies & TV shows in 4K quality. Best IPTV subscription service with 99.9% uptime and 24/7 support.',
    keywords: 'IPTV Premium, IPTV subscription, premium IPTV, live TV streaming, sports IPTV, movie channels, 4K IPTV, IPTV service, streaming TV',
    ogTitle: 'IPTV Premium - Best IPTV Subscription Service 2025',
    ogDescription: 'Stream 15,000+ premium channels, live sports, movies & shows in 4K. Get the ultimate IPTV experience with our premium subscription.',
    ogImage: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=1200&h=630&fit=crop',
    structuredData: {
      '@context': 'https://schema.org',
      '@type': 'Service',
      name: 'IPTV Premium Streaming Service',
      description: 'Premium IPTV streaming service with 15,000+ channels, live sports, movies and TV shows in 4K quality',
      serviceType: 'IPTV Streaming',
      provider: {
        '@type': 'Organization',
        name: 'IPTV Premium',
        url: 'https://iptvpremium.com',
      },
      offers: [
        {
          '@type': 'Offer',
          name: 'Basic IPTV Plan',
          price: '15',
          priceCurrency: 'USD',
          priceValidUntil: '2025-12-31',
          description: '5,000+ channels with HD quality'
        },
        {
          '@type': 'Offer',
          name: 'Premium IPTV Plan',
          price: '25',
          priceCurrency: 'USD',
          priceValidUntil: '2025-12-31',
          description: '10,000+ channels with 4K quality and sports'
        }
      ],
      audience: {
        '@type': 'Audience',
        audienceType: 'IPTV subscribers, sports fans, movie lovers'
      }
    },
  });

  return (
    <div className="min-h-screen bg-black text-netflix-white scroll-smooth">
      <Navigation />
      <HeroSection />
      <ChannelShowcase />
      <SportsShowcase />
      <FeaturesSection />
      <PricingSection />
      <DocumentationSection />
      <TestimonialsSection />
      <ContactSection />
      <Footer />
    </div>
  );
}
