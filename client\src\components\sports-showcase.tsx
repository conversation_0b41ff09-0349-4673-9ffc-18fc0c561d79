import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Play, Clock, Users } from "lucide-react";

export function SportsShowcase() {
  const liveEvents = [
    {
      id: 1,
      title: "Premier League: Manchester United vs Liverpool",
      category: "Football",
      time: "Live Now",
      viewers: "2.5M",
      image: "https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?q=80&w=800&h=600&auto=format&fit=crop",
      isLive: true
    },
    {
      id: 2,
      title: "NBA Finals: Lakers vs Celtics",
      category: "Basketball",
      time: "2 hours",
      viewers: "1.8M",
      image: "https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=800&h=600&auto=format&fit=crop",
      isLive: false
    },
    {
      id: 3,
      title: "Formula 1: Monaco Grand Prix",
      category: "Motor Sports",
      time: "30 mins",
      viewers: "1.2M",
      image: "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?q=80&w=800&h=600&auto=format&fit=crop",
      isLive: false
    },
    {
      id: 4,
      title: "UFC 300: Main Event",
      category: "MMA",
      time: "Live Now",
      viewers: "950K",
      image: "https://images.unsplash.com/photo-1544737151-6e4b9d5b3b5a?q=80&w=800&h=600&auto=format&fit=crop",
      isLive: true
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-b from-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-white">
            Live <span className="text-primary">Sports Events</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Never miss your favorite sports with our comprehensive live coverage. 
            From Premier League to UFC, we have it all in crystal clear quality.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {liveEvents.map((event) => (
            <Card key={event.id} className="group hover:shadow-2xl transition-all duration-300 overflow-hidden bg-gray-800 border-gray-700 hover:border-primary/50">
              <div className="relative">
                <img 
                  src={event.image}
                  alt={event.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />
                
                {/* Live indicator */}
                {event.isLive && (
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-red-600 text-white animate-pulse">
                      <div className="w-2 h-2 bg-white rounded-full mr-2" />
                      LIVE
                    </Badge>
                  </div>
                )}

                {/* Category badge */}
                <div className="absolute top-4 right-4">
                  <Badge variant="secondary" className="bg-black/50 text-white border-gray-600">
                    {event.category}
                  </Badge>
                </div>

                {/* Play button overlay */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-primary rounded-full p-4 transform scale-75 group-hover:scale-100 transition-transform duration-300">
                    <Play className="w-8 h-8 text-white" />
                  </div>
                </div>

                {/* Event info overlay */}
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center text-sm">
                      <Clock className="w-4 h-4 mr-1" />
                      {event.time}
                    </div>
                    <div className="flex items-center text-sm">
                      <Users className="w-4 h-4 mr-1" />
                      {event.viewers}
                    </div>
                  </div>
                </div>
              </div>

              <CardContent className="p-4 bg-gray-800">
                <h3 className="font-bold text-white text-sm line-clamp-2">{event.title}</h3>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Sports categories */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold mb-8 text-white">All Sports Covered</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {[
              { name: "⚽ Football", leagues: "Premier League, La Liga, Serie A" },
              { name: "🏀 Basketball", leagues: "NBA, EuroLeague" },
              { name: "🏎️ Formula 1", leagues: "All Races & Qualifying" },
              { name: "🥊 Boxing/MMA", leagues: "UFC, Boxing Championships" },
              { name: "🎾 Tennis", leagues: "Grand Slams, ATP, WTA" },
              { name: "🏈 American Football", leagues: "NFL, College Football" }
            ].map((sport, index) => (
              <div key={index} className="bg-gray-800 border border-gray-700 rounded-lg p-4 hover:border-primary transition-colors">
                <div className="font-semibold text-white text-sm">{sport.name}</div>
                <div className="text-xs text-gray-400 mt-1">{sport.leagues}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}