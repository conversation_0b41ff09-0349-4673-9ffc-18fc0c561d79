import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLanguage } from '@/hooks/use-language';

export function LanguageSelector() {
  const { currentLanguage, changeLanguage, supportedLanguages, isLoading } = useLanguage();

  const handleLanguageChange = (language: string) => {
    changeLanguage(language);
  };

  return (
    <Select value={currentLanguage} onValueChange={handleLanguageChange} disabled={isLoading}>
      <SelectTrigger className="bg-netflix-black border-netflix-grey text-netflix-white w-[160px]">
        <SelectValue>
          {supportedLanguages.find(lang => lang.code === currentLanguage)?.flag}{' '}
          {supportedLanguages.find(lang => lang.code === currentLanguage)?.name}
        </SelectValue>
      </SelectTrigger>
      <SelectContent className="bg-netflix-black border-netflix-grey">
        {supportedLanguages.map((language) => (
          <SelectItem 
            key={language.code} 
            value={language.code}
            className="text-netflix-white hover:bg-netflix-grey focus:bg-netflix-grey"
          >
            {language.flag} {language.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
