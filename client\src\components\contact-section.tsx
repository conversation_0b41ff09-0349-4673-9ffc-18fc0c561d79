import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useLanguage } from '@/hooks/use-language';
import { useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Mail, Clock, Users, Shield, Send, Loader2 } from 'lucide-react';

interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  message: string;
  language: string;
}

export function ContactSection() {
  const { t, currentLanguage } = useLanguage();
  const { toast } = useToast();
  const [formData, setFormData] = useState<ContactFormData>({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    message: '',
    language: currentLanguage,
  });
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const contactMutation = useMutation({
    mutationFn: async (data: ContactFormData) => {
      return apiRequest('POST', '/api/contact', data);
    },
    onSuccess: () => {
      toast({
        title: t('common.success'),
        description: 'Your message has been sent successfully!',
      });
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        company: '',
        message: '',
        language: currentLanguage,
      });
      setAgreedToTerms(false);
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: 'Failed to send message. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!agreedToTerms) {
      toast({
        title: t('common.error'),
        description: 'Please agree to the terms and conditions.',
        variant: 'destructive',
      });
      return;
    }

    contactMutation.mutate(formData);
  };

  const handleInputChange = (field: keyof ContactFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value,
    }));
  };

  return (
    <section id="contact" className="py-24 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8">
            <div>
              <h2 className="text-4xl font-bold mb-6">{t('contact.title')}</h2>
              <p className="text-xl text-muted-foreground leading-relaxed">
                {t('contact.subtitle')}
              </p>
            </div>
            
            {/* Contact Information */}
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="bg-primary w-12 h-12 rounded-full flex items-center justify-center">
                  <Mail className="w-6 h-6 text-primary-foreground" />
                </div>
                <div>
                  <div className="font-semibold">{t('contact.email')}</div>
                  <div className="text-muted-foreground text-sm">{t('contact.email.desc')}</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="bg-primary w-12 h-12 rounded-full flex items-center justify-center">
                  <i className="fab fa-telegram text-primary-foreground"></i>
                </div>
                <div>
                  <div className="font-semibold">{t('contact.telegram')}</div>
                  <div className="text-muted-foreground text-sm">{t('contact.telegram.desc')}</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="bg-primary w-12 h-12 rounded-full flex items-center justify-center">
                  <Clock className="w-6 h-6 text-primary-foreground" />
                </div>
                <div>
                  <div className="font-semibold">{t('contact.support')}</div>
                  <div className="text-muted-foreground text-sm">{t('contact.support.desc')}</div>
                </div>
              </div>
            </div>
            
            {/* Trust Signals */}
            <div className="space-y-4">
              <div className="flex items-center text-sm text-muted-foreground">
                <Shield className="w-5 h-5 text-success-green mr-3" />
                <span>{t('contact.trust.security')}</span>
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <Clock className="w-5 h-5 text-success-green mr-3" />
                <span>{t('contact.trust.response')}</span>
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <Users className="w-5 h-5 text-success-green mr-3" />
                <span>{t('contact.trust.customers')}</span>
              </div>
            </div>
          </div>
          
          {/* Contact Form */}
          <Card className="glass-effect border-netflix-grey">
            <CardContent className="p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="firstName">{t('contact.form.firstName')}</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange('firstName')}
                      className="bg-netflix-black border-netflix-grey"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">{t('contact.form.lastName')}</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange('lastName')}
                      className="bg-netflix-black border-netflix-grey"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="email">{t('contact.form.email')}</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    className="bg-netflix-black border-netflix-grey"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="company">{t('contact.form.company')}</Label>
                  <Input
                    id="company"
                    value={formData.company}
                    onChange={handleInputChange('company')}
                    className="bg-netflix-black border-netflix-grey"
                  />
                </div>
                
                <div>
                  <Label htmlFor="message">{t('contact.form.message')}</Label>
                  <Textarea
                    id="message"
                    rows={4}
                    value={formData.message}
                    onChange={handleInputChange('message')}
                    className="bg-netflix-black border-netflix-grey"
                    required
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="terms"
                    checked={agreedToTerms}
                    onCheckedChange={setAgreedToTerms}
                  />
                  <Label htmlFor="terms" className="text-sm text-muted-foreground">
                    {t('contact.form.privacy')}
                  </Label>
                </div>
                
                <Button 
                  type="submit" 
                  className="w-full bg-primary hover:bg-primary/90 pulse-glow"
                  disabled={contactMutation.isPending}
                >
                  {contactMutation.isPending ? (
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  ) : (
                    <Send className="w-5 h-5 mr-2" />
                  )}
                  {t('contact.form.submit')}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
