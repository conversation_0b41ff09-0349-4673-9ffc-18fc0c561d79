import { useState, useEffect } from 'react';
import { useRoute } from 'wouter';
import { Navigation } from '@/components/navigation';
import { Footer } from '@/components/footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useSEO } from '@/hooks/use-seo';
import { useLanguage } from '@/hooks/use-language';
import { useQuery } from '@tanstack/react-query';
import { DOCUMENTATION_CATEGORIES } from '@/lib/constants';
import { 
  Search, 
  Clock, 
  Star, 
  Languages, 
  ArrowLeft, 
  BookOpen, 
  Play,
  Download,
  ExternalLink,
  CheckCircle
} from 'lucide-react';

interface DocumentationArticle {
  id: number;
  title: string;
  slug: string;
  content: string;
  category: string;
  language: string;
  difficulty: string;
  estimatedTime: number;
  published: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function Documentation() {
  const { t, currentLanguage } = useLanguage();
  const [match, params] = useRoute('/documentation/:slug');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  
  const slug = params?.slug;
  const isArticleView = !!slug;

  // Fetch documentation list
  const { data: documentation = [], isLoading } = useQuery({
    queryKey: ['/api/documentation', currentLanguage, selectedCategory !== 'all' ? selectedCategory : undefined],
    enabled: !isArticleView,
  });

  // Fetch single article
  const { data: article, isLoading: isArticleLoading } = useQuery({
    queryKey: [`/api/documentation/${slug}`, currentLanguage],
    enabled: isArticleView,
  });

  // Filter documentation based on search
  const filteredDocs = documentation.filter((doc: DocumentationArticle) =>
    doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useSEO({
    title: isArticleView 
      ? `${article?.title || 'Article'} - Documentation - IPTV Premium`
      : `${t('nav.documentation')} - IPTV Premium`,
    description: isArticleView
      ? `${article?.content?.substring(0, 160) || 'IPTV Premium documentation article'}`
      : 'Comprehensive IPTV Premium documentation with step-by-step tutorials for all devices and platforms.',
    keywords: 'IPTV Premium documentation, tutorials, setup guides, Android TV, iOS, Smart TV, PC, Mac',
    structuredData: {
      '@context': 'https://schema.org',
      '@type': isArticleView ? 'Article' : 'CollectionPage',
      name: isArticleView ? article?.title : 'IPTV Premium Documentation',
      description: isArticleView 
        ? article?.content?.substring(0, 200)
        : 'Complete documentation and tutorials for IPTV Premium',
      ...(isArticleView && {
        articleBody: article?.content,
        author: {
          '@type': 'Organization',
          name: 'IPTV Premium',
        },
        publisher: {
          '@type': 'Organization',
          name: 'IPTV Premium',
        },
        datePublished: article?.createdAt,
        dateModified: article?.updatedAt,
      }),
    },
  });

  if (isArticleView) {
    return (
      <div className="min-h-screen bg-black text-netflix-white">
        <Navigation />
        <div className="pt-24 pb-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {isArticleLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              </div>
            ) : article ? (
              <>
                {/* Article Header */}
                <div className="mb-8">
                  <Button 
                    variant="ghost" 
                    className="mb-4 text-muted-foreground hover:text-primary"
                    onClick={() => window.history.back()}
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Documentation
                  </Button>
                  
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Badge variant="secondary" className="bg-netflix-grey">
                        {DOCUMENTATION_CATEGORIES.find(cat => cat.id === article.category)?.name}
                      </Badge>
                      <Badge 
                        variant="outline" 
                        className={`border-current ${
                          article.difficulty === 'beginner' ? 'text-success-green' :
                          article.difficulty === 'intermediate' ? 'text-yellow-500' :
                          'text-primary'
                        }`}
                      >
                        {article.difficulty}
                      </Badge>
                    </div>
                    
                    <h1 className="text-4xl font-bold">{article.title}</h1>
                    
                    <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-2" />
                        <span>{article.estimatedTime} minutes</span>
                      </div>
                      <div className="flex items-center">
                        <Languages className="w-4 h-4 mr-2" />
                        <span>Available in 15 languages</span>
                      </div>
                      <div className="flex items-center">
                        <BookOpen className="w-4 h-4 mr-2" />
                        <span>Step-by-step guide</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Article Content */}
                <Card className="glass-effect border-netflix-grey">
                  <CardContent className="p-8">
                    <div 
                      className="prose prose-invert max-w-none"
                      dangerouslySetInnerHTML={{ __html: article.content }}
                    />
                  </CardContent>
                </Card>

                {/* Article Footer */}
                <div className="mt-8 p-6 glass-effect rounded-xl border border-netflix-grey">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold mb-2">Need Additional Help?</h3>
                      <p className="text-muted-foreground text-sm">
                        Contact our support team for personalized assistance with your setup.
                      </p>
                    </div>
                    <div className="flex space-x-3">
                      <Button variant="outline" size="sm">
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Contact Support
                      </Button>
                      <Button variant="default" size="sm" className="bg-primary hover:bg-primary/90">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Mark as Complete
                      </Button>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <h2 className="text-2xl font-bold mb-4">Article Not Found</h2>
                <p className="text-muted-foreground mb-6">
                  The documentation article you're looking for doesn't exist or has been moved.
                </p>
                <Button onClick={() => window.history.back()}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Documentation
                </Button>
              </div>
            )}
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-netflix-white">
      <Navigation />
      <div className="pt-24 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-6">{t('docs.title')}</h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {t('docs.subtitle')}
            </p>
          </div>

          {/* Search and Filters */}
          <div className="mb-12">
            <div className="max-w-2xl mx-auto">
              <div className="relative mb-6">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <Input
                  placeholder="Search documentation..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-netflix-black border-netflix-grey"
                />
              </div>
              
              <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
                <TabsList className="grid w-full grid-cols-5 bg-netflix-black border border-netflix-grey">
                  <TabsTrigger value="all" className="data-[state=active]:bg-primary">All</TabsTrigger>
                  {DOCUMENTATION_CATEGORIES.map((category) => (
                    <TabsTrigger 
                      key={category.id} 
                      value={category.id}
                      className="data-[state=active]:bg-primary"
                    >
                      {category.name}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>
          </div>

          {/* Quick Start Guide */}
          <Card className="glass-effect border-netflix-grey mb-12">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Play className="w-6 h-6 text-primary mr-3" />
                Quick Start Guide
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center p-4 rounded-lg bg-netflix-black/50">
                  <div className="text-3xl mb-3">📱</div>
                  <h3 className="font-semibold mb-2">1. Choose Device</h3>
                  <p className="text-sm text-muted-foreground">Select your device type from our comprehensive guides</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-netflix-black/50">
                  <div className="text-3xl mb-3">⬇️</div>
                  <h3 className="font-semibold mb-2">2. Download App</h3>
                  <p className="text-sm text-muted-foreground">Get the recommended IPTV application for your device</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-netflix-black/50">
                  <div className="text-3xl mb-3">⚙️</div>
                  <h3 className="font-semibold mb-2">3. Configure</h3>
                  <p className="text-sm text-muted-foreground">Follow our step-by-step configuration instructions</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-netflix-black/50">
                  <div className="text-3xl mb-3">✅</div>
                  <h3 className="font-semibold mb-2">4. Enjoy</h3>
                  <p className="text-sm text-muted-foreground">Start streaming with your premium IPTV service</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Documentation Grid */}
          {isLoading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="glass-effect border-netflix-grey animate-pulse">
                  <CardContent className="p-6">
                    <div className="h-4 bg-netflix-grey rounded mb-4"></div>
                    <div className="h-6 bg-netflix-grey rounded mb-3"></div>
                    <div className="h-16 bg-netflix-grey rounded mb-4"></div>
                    <div className="flex space-x-2">
                      <div className="h-6 w-16 bg-netflix-grey rounded"></div>
                      <div className="h-6 w-20 bg-netflix-grey rounded"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredDocs.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDocs.map((doc: DocumentationArticle) => (
                <Card key={doc.id} className="glass-effect border-netflix-grey card-hover group">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <Badge variant="secondary" className="bg-netflix-grey">
                        {DOCUMENTATION_CATEGORIES.find(cat => cat.id === doc.category)?.name}
                      </Badge>
                      <Badge 
                        variant="outline" 
                        className={`border-current ${
                          doc.difficulty === 'beginner' ? 'text-success-green' :
                          doc.difficulty === 'intermediate' ? 'text-yellow-500' :
                          'text-primary'
                        }`}
                      >
                        {doc.difficulty}
                      </Badge>
                    </div>
                    
                    <h3 className="text-lg font-semibold mb-3 group-hover:text-primary transition-colors">
                      {doc.title}
                    </h3>
                    
                    <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                      {doc.content.replace(/<[^>]*>/g, '').substring(0, 120)}...
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        <span>{doc.estimatedTime} min</span>
                      </div>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 mr-1" />
                        <span>4.8</span>
                      </div>
                    </div>
                    
                    <Button 
                      variant="outline" 
                      className="w-full border-netflix-grey hover:border-primary hover:bg-primary hover:text-primary-foreground"
                      onClick={() => window.location.href = `/documentation/${doc.slug}`}
                    >
                      Read Tutorial
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No Documentation Found</h3>
              <p className="text-muted-foreground">
                {searchQuery 
                  ? `No articles match your search "${searchQuery}"`
                  : 'No documentation available for this category'
                }
              </p>
            </div>
          )}

          {/* Popular Articles */}
          <div className="mt-16">
            <h2 className="text-2xl font-bold mb-8">Popular Articles</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="glass-effect border-netflix-grey">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="bg-primary/20 p-3 rounded-lg">
                      <i className="fab fa-android text-primary text-2xl"></i>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold mb-2">Android TV Complete Setup</h3>
                      <p className="text-muted-foreground text-sm mb-3">
                        Comprehensive guide to install and configure IPTV on Android TV devices
                      </p>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="w-4 h-4 mr-1" />
                        <span>8 minutes</span>
                        <span className="mx-2">•</span>
                        <span>Beginner</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-effect border-netflix-grey">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="bg-primary/20 p-3 rounded-lg">
                      <i className="fab fa-apple text-primary text-2xl"></i>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold mb-2">iOS & Apple TV Setup</h3>
                      <p className="text-muted-foreground text-sm mb-3">
                        Step-by-step instructions for iPhone, iPad, and Apple TV configuration
                      </p>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="w-4 h-4 mr-1" />
                        <span>6 minutes</span>
                        <span className="mx-2">•</span>
                        <span>Beginner</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
