import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";
import { insertContactSubmissionSchema, insertCodeSnippetSchema, insertTranslationSchema } from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // Get translations
  app.get("/api/translations/:language", async (req, res) => {
    try {
      const { language } = req.params;
      const { section } = req.query;
      
      const translations = await storage.getTranslations(language, section as string);
      
      // Convert array to key-value object for easier frontend usage
      const translationsObj = translations.reduce((acc, t) => {
        acc[t.key] = t.value;
        return acc;
      }, {} as Record<string, string>);
      
      res.json(translationsObj);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch translations" });
    }
  });

  // Get code snippets for injection
  app.get("/api/code-snippets", async (req, res) => {
    try {
      const { type } = req.query;
      
      let snippets;
      if (type) {
        snippets = await storage.getCodeSnippetsByType(type as string);
      } else {
        snippets = await storage.getCodeSnippets();
      }
      
      res.json(snippets);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch code snippets" });
    }
  });

  // Create code snippet (admin only)
  app.post("/api/code-snippets", async (req, res) => {
    try {
      const validation = insertCodeSnippetSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ message: "Invalid data", errors: validation.error.errors });
      }
      
      const snippet = await storage.createCodeSnippet(validation.data);
      res.status(201).json(snippet);
    } catch (error) {
      res.status(500).json({ message: "Failed to create code snippet" });
    }
  });

  // Update code snippet (admin only)
  app.put("/api/code-snippets/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const snippet = await storage.updateCodeSnippet(id, req.body);
      res.json(snippet);
    } catch (error) {
      res.status(500).json({ message: "Failed to update code snippet" });
    }
  });

  // Delete code snippet (admin only)
  app.delete("/api/code-snippets/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteCodeSnippet(id);
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: "Failed to delete code snippet" });
    }
  });

  // Submit contact form
  app.post("/api/contact", async (req, res) => {
    try {
      const validation = insertContactSubmissionSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ message: "Invalid data", errors: validation.error.errors });
      }
      
      const submission = await storage.createContactSubmission(validation.data);
      
      // TODO: Send email notification
      // TODO: Send Telegram notification
      
      res.status(201).json({ message: "Contact form submitted successfully", id: submission.id });
    } catch (error) {
      res.status(500).json({ message: "Failed to submit contact form" });
    }
  });

  // Get contact submissions (admin only)
  app.get("/api/contact-submissions", async (req, res) => {
    try {
      const submissions = await storage.getContactSubmissions();
      res.json(submissions);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch contact submissions" });
    }
  });

  // Update contact submission status (admin only)
  app.put("/api/contact-submissions/:id/status", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { status } = req.body;
      
      const submission = await storage.updateContactSubmissionStatus(id, status);
      res.json(submission);
    } catch (error) {
      res.status(500).json({ message: "Failed to update submission status" });
    }
  });

  // Get keywords for SEO tracking
  app.get("/api/keywords", async (req, res) => {
    try {
      const { language } = req.query;
      const keywords = await storage.getKeywords(language as string);
      res.json(keywords);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch keywords" });
    }
  });

  // Get analytics data
  app.get("/api/analytics", async (req, res) => {
    try {
      const { language } = req.query;
      const analytics = await storage.getAnalytics(language as string);
      res.json(analytics);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch analytics" });
    }
  });

  // Get documentation
  app.get("/api/documentation", async (req, res) => {
    try {
      const { language = 'en', category } = req.query;
      const docs = await storage.getDocumentation(language as string, category as string);
      res.json(docs);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch documentation" });
    }
  });

  // Get single documentation article
  app.get("/api/documentation/:slug", async (req, res) => {
    try {
      const { slug } = req.params;
      const { language = 'en' } = req.query;
      
      const doc = await storage.getDocumentationBySlug(slug, language as string);
      
      if (!doc) {
        return res.status(404).json({ message: "Documentation not found" });
      }
      
      res.json(doc);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch documentation" });
    }
  });

  // Create translation (admin only)
  app.post("/api/translations", async (req, res) => {
    try {
      const validation = insertTranslationSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ message: "Invalid data", errors: validation.error.errors });
      }
      
      const translation = await storage.createTranslation(validation.data);
      res.status(201).json(translation);
    } catch (error) {
      res.status(500).json({ message: "Failed to create translation" });
    }
  });

  // Health check endpoint
  app.get("/api/health", (req, res) => {
    res.json({ status: "ok", timestamp: new Date().toISOString() });
  });

  const httpServer = createServer(app);
  return httpServer;
}
