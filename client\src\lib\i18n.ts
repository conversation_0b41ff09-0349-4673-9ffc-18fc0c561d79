import { DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES } from './constants';

export interface Translation {
  [key: string]: string;
}

class I18nService {
  private translations: Record<string, Translation> = {};
  private currentLanguage: string = DEFAULT_LANGUAGE;
  private fallbackLanguage: string = DEFAULT_LANGUAGE;

  constructor() {
    // Initialize with saved language from localStorage
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage && this.isValidLanguage(savedLanguage)) {
      this.currentLanguage = savedLanguage;
    }
  }

  private isValidLanguage(language: string): boolean {
    return SUPPORTED_LANGUAGES.some(lang => lang.code === language);
  }

  async loadTranslations(language: string): Promise<void> {
    if (!this.isValidLanguage(language)) {
      console.warn(`Invalid language: ${language}, falling back to ${this.fallbackLanguage}`);
      language = this.fallbackLanguage;
    }

    try {
      const response = await fetch(`/api/translations/${language}`);
      if (!response.ok) {
        throw new Error(`Failed to load translations for ${language}`);
      }
      
      const translations = await response.json();
      this.translations[language] = translations;
    } catch (error) {
      console.error('Failed to load translations:', error);
      
      // Load fallback translations if not already loaded
      if (language !== this.fallbackLanguage && !this.translations[this.fallbackLanguage]) {
        await this.loadFallbackTranslations();
      }
    }
  }

  private async loadFallbackTranslations(): Promise<void> {
    // Default English translations as fallback
    this.translations[this.fallbackLanguage] = {
      // Navigation
      'nav.features': 'Features',
      'nav.pricing': 'Pricing',
      'nav.documentation': 'Documentation',
      'nav.contact': 'Contact',
      'nav.admin': 'Admin',
      
      // Hero Section
      'hero.title': 'Premium IPTV Management Platform',
      'hero.subtitle': 'Professional subscription management with multi-language support, advanced SEO optimization, and comprehensive analytics dashboard.',
      'hero.cta.primary': 'Start Free Trial',
      'hero.cta.secondary': 'Watch Demo',
      'hero.trust.ssl': 'SSL Secured',
      'hero.trust.languages': '15 Languages',
      'hero.trust.seo': 'SEO Optimized',
      
      // Features Section
      'features.title': 'Comprehensive Platform Features',
      'features.subtitle': 'Everything you need to manage premium IPTV subscriptions with advanced SEO optimization and multi-language support.',
      
      // Pricing Section
      'pricing.title': 'Choose Your Premium Plan',
      'pricing.subtitle': 'Transparent pricing with no hidden fees. Start your free trial today.',
      'pricing.cta': 'Start Free Trial',
      'pricing.contact': 'Contact Sales',
      'pricing.popular': 'Most Popular',
      
      // Documentation Section
      'docs.title': 'Comprehensive Documentation',
      'docs.subtitle': 'Step-by-step tutorials for all devices and platforms with beautiful visual guides',
      'docs.cta': 'View Tutorial',
      'docs.featured.title': 'Featured: Android TV Setup',
      'docs.featured.description': 'Complete step-by-step guide to install and configure IPTV Premium on your Android TV device. Includes troubleshooting tips and optimization settings for the best streaming experience.',
      'docs.featured.time': 'Estimated time: 5-10 minutes',
      'docs.featured.difficulty': 'Difficulty: Beginner',
      'docs.featured.languages': 'Available in 15 languages',
      'docs.featured.cta': 'Start Tutorial',
      
      // Testimonials Section
      'testimonials.title': 'What Our Customers Say',
      'testimonials.subtitle': 'Trusted by thousands of businesses worldwide',
      
      // Contact Section
      'contact.title': 'Get Started Today',
      'contact.subtitle': 'Ready to transform your IPTV business with our comprehensive platform? Contact our team for a personalized demo and consultation.',
      'contact.email': '<EMAIL>',
      'contact.email.desc': 'Email us anytime',
      'contact.telegram': '@IPTVPremiumSupport',
      'contact.telegram.desc': 'Telegram support channel',
      'contact.support': '24/7 Support',
      'contact.support.desc': 'Always here to help',
      'contact.form.firstName': 'First Name',
      'contact.form.lastName': 'Last Name',
      'contact.form.email': 'Email Address',
      'contact.form.company': 'Company',
      'contact.form.message': 'Message',
      'contact.form.privacy': 'I agree to the Privacy Policy and Terms of Service',
      'contact.form.submit': 'Send Message',
      'contact.trust.security': 'Enterprise-grade security and compliance',
      'contact.trust.response': 'Response time under 2 hours',
      'contact.trust.customers': 'Trusted by 10,000+ businesses worldwide',
      
      // Footer
      'footer.company.description': 'Professional IPTV subscription management platform with advanced SEO optimization and multi-language support.',
      'footer.product': 'Product',
      'footer.support': 'Support',
      'footer.legal': 'Legal',
      'footer.copyright': '© 2024 IPTV Premium. All rights reserved.',
      
      // Common
      'common.loading': 'Loading...',
      'common.error': 'An error occurred',
      'common.tryAgain': 'Try Again',
      'common.success': 'Success',
      'common.cancel': 'Cancel',
      'common.save': 'Save',
      'common.delete': 'Delete',
      'common.edit': 'Edit',
      'common.close': 'Close',
    };
  }

  translate(key: string, params?: Record<string, string>): string {
    const translation = this.translations[this.currentLanguage]?.[key] || 
                      this.translations[this.fallbackLanguage]?.[key] || 
                      key;
    
    if (params) {
      return Object.keys(params).reduce((str, param) => {
        return str.replace(new RegExp(`{{${param}}}`, 'g'), params[param]);
      }, translation);
    }
    
    return translation;
  }

  async setLanguage(language: string): Promise<void> {
    if (!this.isValidLanguage(language)) {
      console.warn(`Invalid language: ${language}`);
      return;
    }

    this.currentLanguage = language;
    localStorage.setItem('language', language);
    
    // Load translations if not already loaded
    if (!this.translations[language]) {
      await this.loadTranslations(language);
    }
  }

  getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  getSupportedLanguages() {
    return SUPPORTED_LANGUAGES;
  }

  isTranslationLoaded(language: string): boolean {
    return !!this.translations[language];
  }
}

export const i18n = new I18nService();
