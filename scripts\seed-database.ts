import { db } from "../server/db";
import { users, translations, documentation, codeSnippets, keywords } from "../shared/schema";

const languages = [
  'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ar', 'zh', 'ja', 'ko', 'hi', 'tr', 'pl', 'nl'
];

const baseTranslations = {
  // Hero Section
  'hero.title': 'Premium IPTV Service - 4K Streaming',
  'hero.subtitle': 'Watch 10,000+ channels, sports, movies & TV shows in Ultra HD quality',
  'hero.cta': 'Start Free Trial',
  'hero.features': 'No Contract • 24/7 Support • Money Back Guarantee',
  
  // Navigation
  'nav.home': 'Home',
  'nav.channels': 'Channels',
  'nav.sports': 'Sports',
  'nav.movies': 'Movies',
  'nav.pricing': 'Pricing',
  'nav.support': 'Support',
  'nav.contact': 'Contact',
  
  // Features
  'features.title': 'Why Choose Our IPTV Service?',
  'features.hd_quality': '4K Ultra HD Quality',
  'features.hd_desc': 'Crystal clear streaming in 4K resolution',
  'features.channels': '10,000+ Channels',
  'features.channels_desc': 'Global channels from every country',
  'features.sports': 'Live Sports',
  'features.sports_desc': 'All major sports leagues and events',
  'features.support': '24/7 Support',
  'features.support_desc': 'Round-the-clock customer assistance',
  
  // Pricing
  'pricing.title': 'Choose Your Plan',
  'pricing.basic': 'Basic Plan',
  'pricing.premium': 'Premium Plan',
  'pricing.ultimate': 'Ultimate Plan',
  'pricing.monthly': '/month',
  'pricing.select': 'Select Plan',
  
  // Contact
  'contact.title': 'Contact Us',
  'contact.subtitle': 'Get in touch with our support team',
  'contact.name': 'Full Name',
  'contact.email': 'Email Address',
  'contact.company': 'Company (Optional)',
  'contact.message': 'Message',
  'contact.send': 'Send Message',
  
  // Footer
  'footer.description': 'Premium IPTV streaming service with global channels',
  'footer.links': 'Quick Links',
  'footer.legal': 'Legal',
  'footer.privacy': 'Privacy Policy',
  'footer.terms': 'Terms of Service',
  'footer.copyright': '© 2024 IPTV Premium. All rights reserved.',
};

const translationMap: Record<string, Record<string, string>> = {
  'es': {
    'hero.title': 'Servicio IPTV Premium - Streaming 4K',
    'hero.subtitle': 'Mira 10,000+ canales, deportes, películas y programas de TV en calidad Ultra HD',
    'hero.cta': 'Iniciar Prueba Gratuita',
    'nav.home': 'Inicio',
    'nav.channels': 'Canales',
    'nav.sports': 'Deportes',
    'nav.movies': 'Películas',
    'nav.pricing': 'Precios',
    'nav.support': 'Soporte',
    'nav.contact': 'Contacto',
    'features.title': '¿Por qué elegir nuestro servicio IPTV?',
    'contact.title': 'Contáctanos',
    'contact.name': 'Nombre Completo',
    'contact.email': 'Correo Electrónico',
    'contact.message': 'Mensaje',
    'contact.send': 'Enviar Mensaje',
  },
  'fr': {
    'hero.title': 'Service IPTV Premium - Streaming 4K',
    'hero.subtitle': 'Regardez 10 000+ chaînes, sports, films et émissions TV en qualité Ultra HD',
    'hero.cta': 'Commencer l\'Essai Gratuit',
    'nav.home': 'Accueil',
    'nav.channels': 'Chaînes',
    'nav.sports': 'Sports',
    'nav.movies': 'Films',
    'nav.pricing': 'Tarifs',
    'nav.support': 'Support',
    'nav.contact': 'Contact',
    'features.title': 'Pourquoi choisir notre service IPTV?',
    'contact.title': 'Nous Contacter',
    'contact.name': 'Nom Complet',
    'contact.email': 'Adresse Email',
    'contact.message': 'Message',
    'contact.send': 'Envoyer le Message',
  },
  'de': {
    'hero.title': 'Premium IPTV Service - 4K Streaming',
    'hero.subtitle': 'Schauen Sie 10.000+ Kanäle, Sport, Filme und TV-Shows in Ultra HD Qualität',
    'hero.cta': 'Kostenlose Testversion Starten',
    'nav.home': 'Startseite',
    'nav.channels': 'Kanäle',
    'nav.sports': 'Sport',
    'nav.movies': 'Filme',
    'nav.pricing': 'Preise',
    'nav.support': 'Support',
    'nav.contact': 'Kontakt',
    'features.title': 'Warum unseren IPTV-Service wählen?',
    'contact.title': 'Kontaktieren Sie Uns',
    'contact.name': 'Vollständiger Name',
    'contact.email': 'E-Mail-Adresse',
    'contact.message': 'Nachricht',
    'contact.send': 'Nachricht Senden',
  }
};

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // Create admin user
    console.log('👤 Creating admin user...');
    await db.insert(users).values({
      username: 'admin',
      password: 'admin123', // In production, this should be hashed
      email: '<EMAIL>',
      role: 'admin'
    }).onConflictDoNothing();

    // Create translations for all languages
    console.log('🌍 Creating translations...');
    for (const lang of languages) {
      const translations_data = lang === 'en' ? baseTranslations : {
        ...baseTranslations,
        ...(translationMap[lang] || {})
      };

      for (const [key, value] of Object.entries(translations_data)) {
        await db.insert(translations).values({
          key,
          language: lang,
          value,
          section: key.split('.')[0] // Extract section from key
        }).onConflictDoNothing();
      }
    }

    // Create sample documentation
    console.log('📚 Creating documentation...');
    const docCategories = ['android', 'ios', 'smart-tv', 'pc-mac'];
    
    for (const category of docCategories) {
      await db.insert(documentation).values({
        title: `How to Install IPTV on ${category.toUpperCase()}`,
        slug: `install-${category}`,
        content: `# Installation Guide for ${category.toUpperCase()}\n\nStep-by-step instructions for setting up IPTV on your ${category} device.\n\n## Requirements\n- Stable internet connection\n- Compatible IPTV player app\n\n## Installation Steps\n1. Download the recommended app\n2. Enter your IPTV credentials\n3. Configure settings\n4. Start watching!`,
        category,
        language: 'en',
        difficulty: 'beginner',
        estimatedTime: 15,
        published: true
      }).onConflictDoNothing();
    }

    // Create SEO keywords
    console.log('🔍 Creating SEO keywords...');
    const seoKeywords = [
      'IPTV Premium', 'IPTV Service', '4K IPTV', 'Live TV Streaming',
      'IPTV Channels', 'Sports IPTV', 'Movie Streaming', 'TV Shows Online'
    ];

    for (const keyword of seoKeywords) {
      await db.insert(keywords).values({
        keyword,
        language: 'en',
        currentRank: Math.floor(Math.random() * 100) + 1,
        url: 'https://iptv-premium.com'
      }).onConflictDoNothing();
    }

    // Create sample code snippets
    console.log('💻 Creating code snippets...');
    await db.insert(codeSnippets).values([
      {
        name: 'Google Analytics',
        type: 'head',
        code: '<!-- Google Analytics will be configured here -->',
        enabled: true
      },
      {
        name: 'Facebook Pixel',
        type: 'head',
        code: '<!-- Facebook Pixel will be configured here -->',
        enabled: false
      }
    ]).onConflictDoNothing();

    console.log('✅ Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase().then(() => process.exit(0)).catch(() => process.exit(1));
}

export { seedDatabase };
