import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/hooks/use-language';
import { TrendingUp, TrendingDown, Globe, BarChart3 } from 'lucide-react';

export function DashboardPreview() {
  const { t } = useLanguage();

  const keywordData = [
    { keyword: '"IPTV Premium"', rank: 3, trend: 'up', language: '🇺🇸' },
    { keyword: '"Premium Streaming"', rank: 7, trend: 'up', language: '🇺🇸' },
    { keyword: '"IPTV Subscription"', rank: 12, trend: 'down', language: '🇺🇸' },
  ];

  const languagePerformance = [
    { flag: '🇺🇸', rank: 3, language: 'English' },
    { flag: '🇫🇷', rank: 5, language: 'French' },
    { flag: '🇪🇸', rank: 8, language: 'Spanish' },
  ];

  return (
    <section className="py-24 bg-netflix-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6">Advanced SEO Analytics Dashboard</h2>
          <p className="text-xl text-muted-foreground">
            Beautiful data visualization with real-time insights and competitive analysis
          </p>
        </div>
        
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="w-6 h-6 text-primary mr-3" />
                  Keyword Ranking Tracking
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {keywordData.map((item, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-muted-foreground">{item.keyword}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-success-green">#{item.rank}</span>
                      {item.trend === 'up' ? (
                        <TrendingUp className="w-4 h-4 text-success-green" />
                      ) : (
                        <TrendingDown className="w-4 h-4 text-primary" />
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
            
            <Card className="glass-effect border-netflix-grey">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="w-6 h-6 text-primary mr-3" />
                  Multi-Language Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  {languagePerformance.map((item, index) => (
                    <div key={index} className="text-center">
                      <div className="text-2xl font-bold text-success-green">
                        {item.flag} #{item.rank}
                      </div>
                      <div className="text-sm text-muted-foreground">{item.language}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="relative">
            <img 
              src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=800" 
              alt="SEO analytics dashboard showing keyword rankings and performance metrics" 
              className="rounded-2xl shadow-2xl w-full h-auto" 
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-2xl"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
