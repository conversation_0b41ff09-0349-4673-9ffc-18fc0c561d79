import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

neonConfig.webSocketConstructor = ws;

if (!process.env.DATABASE_URL) {
  console.warn("⚠️  DATABASE_URL not set. Using demo mode with mock data.");
  console.log("🔧 To set up a real database:");
  console.log("   1. Get free database at https://neon.tech");
  console.log("   2. Or run: npm run quick-setup");
  console.log("   3. Update DATABASE_URL in .env file");
}

export const pool = process.env.DATABASE_URL ? new Pool({ connectionString: process.env.DATABASE_URL }) : null;
export const db = pool ? drizzle({ client: pool, schema }) : null;