import { 
  users, translations, codeSnippets, keywords, contactSubmissions, analytics, documentation,
  type User, type InsertUser, type Translation, type InsertTranslation,
  type CodeSnippet, type InsertCodeSnippet, type Keyword, type InsertKeyword,
  type ContactSubmission, type InsertContactSubmission, type Analytics, type InsertAnalytics,
  type Documentation, type InsertDocumentation
} from "@shared/schema";
import { db } from "./db";
import { eq, and, desc, asc } from "drizzle-orm";

export interface IStorage {
  // Users
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Translations
  getTranslations(language: string, section?: string): Promise<Translation[]>;
  getTranslation(key: string, language: string): Promise<Translation | undefined>;
  createTranslation(translation: InsertTranslation): Promise<Translation>;
  updateTranslation(id: number, translation: Partial<InsertTranslation>): Promise<Translation>;
  
  // Code Snippets
  getCodeSnippets(): Promise<CodeSnippet[]>;
  getCodeSnippetsByType(type: string): Promise<CodeSnippet[]>;
  createCodeSnippet(snippet: InsertCodeSnippet): Promise<CodeSnippet>;
  updateCodeSnippet(id: number, snippet: Partial<InsertCodeSnippet>): Promise<CodeSnippet>;
  deleteCodeSnippet(id: number): Promise<void>;
  
  // Keywords
  getKeywords(language?: string): Promise<Keyword[]>;
  createKeyword(keyword: InsertKeyword): Promise<Keyword>;
  updateKeyword(id: number, keyword: Partial<InsertKeyword>): Promise<Keyword>;
  
  // Contact Submissions
  getContactSubmissions(): Promise<ContactSubmission[]>;
  createContactSubmission(submission: InsertContactSubmission): Promise<ContactSubmission>;
  updateContactSubmissionStatus(id: number, status: string): Promise<ContactSubmission>;
  
  // Analytics
  getAnalytics(language?: string, startDate?: Date, endDate?: Date): Promise<Analytics[]>;
  createAnalytics(analytics: InsertAnalytics): Promise<Analytics>;
  
  // Documentation
  getDocumentation(language: string, category?: string): Promise<Documentation[]>;
  getDocumentationBySlug(slug: string, language: string): Promise<Documentation | undefined>;
  createDocumentation(doc: InsertDocumentation): Promise<Documentation>;
  updateDocumentation(id: number, doc: Partial<InsertDocumentation>): Promise<Documentation>;
}

export class DatabaseStorage implements IStorage {
  // Users
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  // Translations
  async getTranslations(language: string, section?: string): Promise<Translation[]> {
    const query = db.select().from(translations).where(eq(translations.language, language));
    
    if (section) {
      return await query.where(and(eq(translations.language, language), eq(translations.section, section)));
    }
    
    return await query;
  }

  async getTranslation(key: string, language: string): Promise<Translation | undefined> {
    const [translation] = await db.select().from(translations)
      .where(and(eq(translations.key, key), eq(translations.language, language)));
    return translation || undefined;
  }

  async createTranslation(translation: InsertTranslation): Promise<Translation> {
    const [newTranslation] = await db.insert(translations).values(translation).returning();
    return newTranslation;
  }

  async updateTranslation(id: number, translation: Partial<InsertTranslation>): Promise<Translation> {
    const [updatedTranslation] = await db.update(translations)
      .set({ ...translation, updatedAt: new Date() })
      .where(eq(translations.id, id))
      .returning();
    return updatedTranslation;
  }

  // Code Snippets
  async getCodeSnippets(): Promise<CodeSnippet[]> {
    return await db.select().from(codeSnippets).orderBy(asc(codeSnippets.name));
  }

  async getCodeSnippetsByType(type: string): Promise<CodeSnippet[]> {
    return await db.select().from(codeSnippets)
      .where(and(eq(codeSnippets.type, type), eq(codeSnippets.enabled, true)))
      .orderBy(asc(codeSnippets.name));
  }

  async createCodeSnippet(snippet: InsertCodeSnippet): Promise<CodeSnippet> {
    const [newSnippet] = await db.insert(codeSnippets).values(snippet).returning();
    return newSnippet;
  }

  async updateCodeSnippet(id: number, snippet: Partial<InsertCodeSnippet>): Promise<CodeSnippet> {
    const [updatedSnippet] = await db.update(codeSnippets)
      .set({ ...snippet, updatedAt: new Date() })
      .where(eq(codeSnippets.id, id))
      .returning();
    return updatedSnippet;
  }

  async deleteCodeSnippet(id: number): Promise<void> {
    await db.delete(codeSnippets).where(eq(codeSnippets.id, id));
  }

  // Keywords
  async getKeywords(language?: string): Promise<Keyword[]> {
    const query = db.select().from(keywords).orderBy(desc(keywords.lastChecked));
    
    if (language) {
      return await query.where(eq(keywords.language, language));
    }
    
    return await query;
  }

  async createKeyword(keyword: InsertKeyword): Promise<Keyword> {
    const [newKeyword] = await db.insert(keywords).values(keyword).returning();
    return newKeyword;
  }

  async updateKeyword(id: number, keyword: Partial<InsertKeyword>): Promise<Keyword> {
    const [updatedKeyword] = await db.update(keywords)
      .set(keyword)
      .where(eq(keywords.id, id))
      .returning();
    return updatedKeyword;
  }

  // Contact Submissions
  async getContactSubmissions(): Promise<ContactSubmission[]> {
    return await db.select().from(contactSubmissions).orderBy(desc(contactSubmissions.createdAt));
  }

  async createContactSubmission(submission: InsertContactSubmission): Promise<ContactSubmission> {
    const [newSubmission] = await db.insert(contactSubmissions).values(submission).returning();
    return newSubmission;
  }

  async updateContactSubmissionStatus(id: number, status: string): Promise<ContactSubmission> {
    const [updatedSubmission] = await db.update(contactSubmissions)
      .set({ status })
      .where(eq(contactSubmissions.id, id))
      .returning();
    return updatedSubmission;
  }

  // Analytics
  async getAnalytics(language?: string, startDate?: Date, endDate?: Date): Promise<Analytics[]> {
    let query = db.select().from(analytics).orderBy(desc(analytics.date));
    
    if (language) {
      query = query.where(eq(analytics.language, language)) as any;
    }
    
    return await query;
  }

  async createAnalytics(analyticsData: InsertAnalytics): Promise<Analytics> {
    const [newAnalytics] = await db.insert(analytics).values(analyticsData).returning();
    return newAnalytics;
  }

  // Documentation
  async getDocumentation(language: string, category?: string): Promise<Documentation[]> {
    let query = db.select().from(documentation)
      .where(and(eq(documentation.language, language), eq(documentation.published, true)))
      .orderBy(asc(documentation.title));
    
    if (category) {
      query = query.where(and(
        eq(documentation.language, language),
        eq(documentation.category, category),
        eq(documentation.published, true)
      )) as any;
    }
    
    return await query;
  }

  async getDocumentationBySlug(slug: string, language: string): Promise<Documentation | undefined> {
    const [doc] = await db.select().from(documentation)
      .where(and(
        eq(documentation.slug, slug),
        eq(documentation.language, language),
        eq(documentation.published, true)
      ));
    return doc || undefined;
  }

  async createDocumentation(doc: InsertDocumentation): Promise<Documentation> {
    const [newDoc] = await db.insert(documentation).values(doc).returning();
    return newDoc;
  }

  async updateDocumentation(id: number, doc: Partial<InsertDocumentation>): Promise<Documentation> {
    const [updatedDoc] = await db.update(documentation)
      .set({ ...doc, updatedAt: new Date() })
      .where(eq(documentation.id, id))
      .returning();
    return updatedDoc;
  }
}

export const storage = new DatabaseStorage();
