import { Navigation } from '@/components/navigation';
import { ContactSection } from '@/components/contact-section';
import { Footer } from '@/components/footer';
import { useSEO } from '@/hooks/use-seo';
import { useLanguage } from '@/hooks/use-language';

export default function Contact() {
  const { t, currentLanguage } = useLanguage();

  useSEO({
    title: `${t('nav.contact')} - IPTV Premium`,
    description: 'Contact our IPTV Premium support team for personalized demos, consultations, and technical assistance. Available 24/7 with enterprise-grade support.',
    keywords: 'IPTV Premium contact, support, customer service, technical assistance, demo request',
    ogTitle: `${t('nav.contact')} - IPTV Premium`,
    ogDescription: 'Get in touch with our IPTV Premium support team for personalized assistance',
    structuredData: {
      '@context': 'https://schema.org',
      '@type': 'ContactPage',
      name: 'Contact IPTV Premium',
      description: 'Contact page for IPTV Premium support and sales',
      provider: {
        '@type': 'Organization',
        name: 'IPTV Premium',
        contactPoint: {
          '@type': 'ContactPoint',
          contactType: 'customer support',
          email: '<EMAIL>',
          availableLanguage: ['en', 'fr', 'es', 'pt', 'nl', 'pl', 'sv', 'it', 'de', 'fi', 'da', 'ro', 'sk', 'sq', 'nb'],
        },
      },
    },
  });

  return (
    <div className="min-h-screen bg-black text-netflix-white">
      <Navigation />
      <div className="pt-16">
        <ContactSection />
      </div>
      <Footer />
    </div>
  );
}
