# IPTV Premium Platform Setup Guide

## Quick Start Options

### Option 1: Using Neon (Recommended for Quick Setup)
1. Go to [neon.tech](https://neon.tech) and create a free account
2. Create a new database project
3. Copy the connection string
4. Update the `DATABASE_URL` in `.env` file

### Option 2: Local PostgreSQL Setup
1. Install PostgreSQL on your system
2. Create a database named `iptv_premium`
3. Update the `.env` file with your local database credentials

### Option 3: Docker PostgreSQL (If you have Docker)
```bash
docker run --name iptv-postgres -e POSTGRES_PASSWORD=password -e POSTGRES_DB=iptv_premium -p 5432:5432 -d postgres:15
```
Then use: `DATABASE_URL=postgresql://postgres:password@localhost:5432/iptv_premium`

## Running the Application

1. Install dependencies:
```bash
npm install
```

2. Set up the database schema:
```bash
npm run db:push
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser to `http://localhost:5000`

## Default Admin Access
- Username: admin
- Password: admin123
- Email: <EMAIL>

## Features Included
✅ Complete IPTV-focused website design
✅ Multi-language support (15 languages)
✅ Admin panel for content management
✅ Contact form system
✅ SEO optimization
✅ Analytics dashboard
✅ Documentation system
✅ Code injection system

## Next Steps
- Populate translations for all languages
- Add more IPTV channel content
- Set up email notifications
- Configure analytics tracking
- Add more documentation articles
